import React from 'react';
import './DeploymentDiagram.less';

export interface DeploymentDiagramProps {
  className?: string;
  onComplete?: () => void;
  autoPlay?: boolean;
  width?: number;
  height?: number;
}

// Base class for deployment diagrams
const DeploymentDiagram: React.FC<DeploymentDiagramProps> = ({
  className,
  width = 800,
  height = 400,
}) => {
  return (
    <div className={`deployment-diagram ${className || ''}`}>
      {/* Base diagram content - should be overridden by specific implementations */}
      <svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
        <text x={width/2} y={height/2} textAnchor="middle">Deployment Diagram Base</text>
      </svg>
    </div>
  );
};

export default DeploymentDiagram; 