/* StrategyConfigTab 样式 */

.strategy-section {
  .section-description {
    margin-bottom: 16px;
  }

  .help-icon {
    color: #1890ff;
    margin-left: 8px;
    cursor: pointer;
  }

  /* 卡片式选择器样式 */
  .strategy-card-selector {
    margin-bottom: 24px;
    
    .strategy-card {
      cursor: pointer;
      transition: all 0.3s ease;
      height: 100%;
      border: 1px solid #e8e8e8;
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
      }
      
      &.selected {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .strategy-card-header {
      margin-bottom: 12px;
      
      .strategy-card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        
        span {
          margin-left: 8px;
        }
        
        .selected-icon {
          color: #1890ff;
          margin-left: auto;
          font-size: 16px;
        }
      }
    }

    .strategy-card-content {
      display: flex;
      justify-content: space-between;
      
      .strategy-card-desc {
        flex: 1;
        
        ul {
          padding-left: 16px;
          margin-bottom: 0;
          
          li {
            font-size: 12px;
            color: #595959;
            margin-bottom: 4px;
            line-height: 1.5;
          }
        }
      }

      .strategy-card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 70px;
      }
    }
  }

  /* 策略图表样式 */
  .strategy-diagram-card {
    margin-bottom: 24px;
    
    .ant-card-head {
      background-color: #fafafa;
      
      .ant-card-head-title {
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .strategy-diagram {
      min-height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
  }

  /* 策略配置部分样式 */
  .strategy-config-section {
    .ant-divider {
      margin-top: 0;
      
      .ant-divider-inner-text {
        font-size: 14px;
        font-weight: 500;
      }
    }
    
    .canary-steps-header {
      margin-bottom: 16px;
      
      .ant-typography + .ant-typography {
        margin-left: 8px;
      }
    }
    
    .canary-steps-container {
      margin-bottom: 16px;
    }
    
    .canary-step-item {
      position: relative;
      margin-bottom: 16px;
      padding: 16px;
      padding-left: 40px;
      background-color: #fafafa;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
      
      .step-number {
        position: absolute;
        left: 12px;
        top: 16px;
        width: 20px;
        height: 20px;
        background-color: #1890ff;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
      }

      /* 手动确认开关样式 */
      .ant-switch {
        background-color: #1890ff;
        
        &.ant-switch-checked {
          background-color: #52c41a;
        }
        
        &.ant-switch-disabled {
          opacity: 0.8;
          cursor: not-allowed;
        }
      }

      /* 删除步骤按钮 */
      .delete-step-btn-col {
        display: flex;
        justify-content: center;
        align-items: center;

        .delete-step-btn {
          margin-top: 22px; /* 与表单标签对齐 */
          
          &.ant-btn-text {
            &:hover:not(:disabled) {
              background-color: #fff1f0;
            }
            
            &:disabled {
              color: #d9d9d9;
              cursor: not-allowed;
            }
          }
        }
      }
    }
    
    /* 添加步骤按钮样式 */
    .add-step-btn {
      width: 100%;
      margin-top: 8px;
      
      &:hover {
        color: #40a9ff;
        border-color: #40a9ff;
      }
      
      .anticon {
        font-size: 12px;
      }
    }
    
    .canary-tips {
      padding: 8px;
      background-color: #f6ffed;
      border-radius: 4px;
      border: 1px solid #d9f7be;
    }
  }
}

/* 响应式调整 */
@media (max-width: 576px) {
  .strategy-section {
    .strategy-card-selector {
      .strategy-card-content {
        flex-direction: column;
        
        .strategy-card-icon {
          margin-top: 8px;
          width: 100%;
        }
      }
    }

    .strategy-config-section {
      .canary-step-item {
        .ant-row {
          .ant-col {
            margin-bottom: 8px;
          }
        }
        
        .delete-step-btn-col {
          text-align: left;
          
          .delete-step-btn {
            margin-top: 0;
          }
        }
      }
    }
  }
} 