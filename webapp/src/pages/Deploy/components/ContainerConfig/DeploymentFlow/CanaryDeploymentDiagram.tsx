import React, { useState, useEffect } from 'react';
import { DeploymentDiagramProps } from './DeploymentDiagram';
import './DeploymentDiagram.less';

// 金丝雀部署图表组件
const CanaryDeploymentDiagram: React.FC<DeploymentDiagramProps> = ({
  className,
  onComplete,
  autoPlay = true,
  width = 800,
  height = 200, // 进一步减小高度
}) => {
  // 动画阶段状态
  const [stage, setStage] = useState(0);
  const [trafficPercentage, setTrafficPercentage] = useState(0);
  
  // 自动播放动画
  useEffect(() => {
    if (!autoPlay) return;
    
    // 各阶段的时间间隔
    const stageDelays = [1000, 3000, 5000];
    
    const timers = stageDelays.map((delay, index) => {
      return setTimeout(() => {
        setStage(index + 1);
        
        // 阶段1：流量从0%增加到20%
        if (index === 0) {
          let percent = 0;
          const trafficInterval = setInterval(() => {
            percent += 1;
            setTrafficPercentage(percent);
            if (percent >= 20) clearInterval(trafficInterval);
          }, 50);
        }
        
        // 阶段2：流量从20%增加到50%
        if (index === 1) {
          let percent = 20;
          const trafficInterval = setInterval(() => {
            percent += 1;
            setTrafficPercentage(percent);
            if (percent >= 50) clearInterval(trafficInterval);
          }, 40);
        }
        
        // 阶段3：流量从50%增加到100%
        if (index === 2) {
          let percent = 50;
          const trafficInterval = setInterval(() => {
            percent += 1;
            setTrafficPercentage(percent);
            if (percent >= 100) {
              clearInterval(trafficInterval);
              // 完成时回调
              if (onComplete) setTimeout(onComplete, 500);
            }
          }, 30);
        }
      }, delay);
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [autoPlay, onComplete]);

  return (
    <div className={`deployment-diagram canary-diagram ${className || ''}`}>
      <svg 
        width="100%" 
        height={height} 
        viewBox="0 0 800 200" 
        preserveAspectRatio="xMidYMid meet"
      >
        {/* 定义渐变和滤镜 */}
        <defs>
          <linearGradient id="canaryGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#FFC069" stopOpacity="1" />
            <stop offset="100%" stopColor="#FA8C16" stopOpacity="1" />
          </linearGradient>
          <linearGradient id="stableGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#40A9FF" stopOpacity="1" />
            <stop offset="100%" stopColor="#1890FF" stopOpacity="1" />
          </linearGradient>
          <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#FFC53D" stopOpacity="1" />
            <stop offset="100%" stopColor="#FA8C16" stopOpacity="1" />
          </linearGradient>
          <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" />
            <feOffset dx="1" dy="2" result="offsetblur" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.2" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceGraphic" stdDeviation="4" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.3" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        
        {/* 流量进度条 */}
        <g className="traffic-meter" transform="translate(150, 15)">
          <rect x="0" y="0" width="450" height="14" rx="7" fill="#F0F0F0" />
          <rect 
            x="0" 
            y="0" 
            width={4.5 * trafficPercentage || 0} 
            height="14" 
            rx="7" 
            fill="url(#progressGradient)" 
            className="progress-bar"
          />
          <text 
            x={4.5 * trafficPercentage + 10 || 10} 
            y="22" 
            textAnchor="middle" 
            fontSize="10" 
            fontWeight="bold" 
            fill="#FA8C16"
          >
            {trafficPercentage}%
          </text>
          
          {/* 重要流量节点标记 */}
          {[
            { pos: 0, active: stage >= 0 },
            { pos: 90, active: stage >= 1 },
            { pos: 225, active: stage >= 2 },
            { pos: 450, active: stage >= 3 }
          ].map((marker, idx) => (
            <g key={idx} transform={`translate(${marker.pos}, 0)`}>
              <circle 
                cy="7" 
                r="4" 
                fill={marker.active ? (idx === 0 ? '#1890FF' : '#FA8C16') : 'white'} 
                stroke={marker.active ? 'none' : '#D9D9D9'}
                strokeWidth="1"
                filter={marker.active ? 'url(#glow)' : 'none'}
              />
            </g>
          ))}
        </g>

        {/* 部署架构图 */}
        <g className="deployment-architecture" transform="translate(0, 40)">
          {/* 用户图标 */}
          <g className="users" transform="translate(80, 70)">
            <circle r="15" fill="#F0F0F0" />
            <text x="0" y="4" textAnchor="middle" fontSize="12">👥</text>
            <text x="0" y="25" textAnchor="middle" fontSize="9" fill="#8C8C8C">用户</text>
          </g>
          
          {/* 路由控制器 */}
          <g className="router" transform="translate(180, 70)">
            <polygon 
              points="-20,-20 20,-20 30,0 20,20 -20,20 -30,0" 
              fill="#F0F0F0" 
              stroke="#D9D9D9" 
              strokeWidth="1"
              filter={stage > 0 ? 'url(#glow)' : 'none'}
            />
            <text x="0" y="4" textAnchor="middle" fontSize="10" fontWeight="bold">流量控制</text>
            <text x="0" y="18" textAnchor="middle" fontSize="8">按比例分配</text>
          </g>

          {/* 当前版本服务器 */}
          <g className="stable-server" transform="translate(350, 30)">
            <rect 
              x="-40" 
              y="-20" 
              width="80" 
              height="50" 
              rx="4" 
              fill="url(#stableGradient)" 
              filter="url(#dropShadow)" 
            />
            <text x="0" y="-5" textAnchor="middle" fill="white" fontSize="10" fontWeight="bold">当前版本</text>
            <text x="0" y="10" textAnchor="middle" fill="white" fontSize="8">稳定运行环境</text>
            
            {/* 服务实例 - 修正矩形表示 */}
            <g transform="translate(0, 22)">
              <rect x="-20" width="10" height="5" fill="white" opacity="0.9" rx="1" />
              <rect x="0" width="10" height="5" fill="white" opacity="0.9" rx="1" />
              <rect x="20" width="10" height="5" fill="white" opacity="0.9" rx="1" />
            </g>
            
            {/* 流量标识 */}
            <g transform="translate(50, 0)">
              <rect x="0" y="-10" width="40" height="18" rx="9" fill="white" stroke="#1890FF" strokeWidth="1" />
              <text x="20" y="3" textAnchor="middle" fontSize="9" fontWeight="bold" fill="#1890FF">
                {100 - trafficPercentage}%
              </text>
            </g>
          </g>

          {/* 金丝雀版本服务器 - 仅在阶段1及以后显示 */}
          <g 
            className={`canary-server ${stage >= 1 ? 'active' : 'hidden'}`}
            transform="translate(350, 110)"
            opacity={stage >= 1 ? 1 : 0}
          >
            <rect 
              x="-40" 
              y="-20" 
              width="80" 
              height="50" 
              rx="4" 
              fill="url(#canaryGradient)" 
              filter="url(#dropShadow)" 
            />
            <text x="0" y="-5" textAnchor="middle" fill="white" fontSize="10" fontWeight="bold">金丝雀版本</text>
            <text x="0" y="10" textAnchor="middle" fill="white" fontSize="8">新特性测试</text>
            
            {/* 修正服务实例 - 矩形表示 */}
            <g transform="translate(0, 22)">
              {stage >= 1 && (
                <rect
                  className={'animated-element delay-1'}
                  x="-20"
                  width="10"
                  height="5"
                  fill="white"
                  opacity="0.9"
                  rx="1"
                />
              )}
              {stage >= 2 && (
                <rect
                  className="animated-element delay-1"
                  x="0"
                  width="10"
                  height="5"
                  fill="white"
                  opacity="0.9"
                  rx="1"
                />
              )}
              {stage >= 3 && (
                <rect
                  className="animated-element delay-2"
                  x="20"
                  width="10"
                  height="5"
                  fill="white"
                  opacity="0.9"
                  rx="1"
                />
              )}
            </g>
            
            {/* 流量标识 */}
            <g transform="translate(50, 0)">
              <rect x="0" y="-10" width="40" height="18" rx="9" fill="white" stroke="#FA8C16" strokeWidth="1" />
              <text x="20" y="3" textAnchor="middle" fontSize="9" fontWeight="bold" fill="#FA8C16">
                {trafficPercentage}%
              </text>
            </g>
          </g>

          {/* 流量路径 - 从用户到路由器 */}
          <path 
            d="M100,70 L145,70" 
            stroke="#666" 
            strokeWidth="2" 
            strokeDasharray="3,2" 
            markerEnd="url(#arrowMain)"
          />
          
          <defs>
            <marker id="arrowMain" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="5" markerHeight="5" orient="auto">
              <path d="M 0 0 L 10 5 L 0 10 z" fill="#666" />
            </marker>
            <marker id="arrowStable" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="5" markerHeight="5" orient="auto">
              <path d="M 0 0 L 10 5 L 0 10 z" fill="#1890FF" />
            </marker>
            <marker id="arrowCanary" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="5" markerHeight="5" orient="auto">
              <path d="M 0 0 L 10 5 L 0 10 z" fill="#FA8C16" />
            </marker>
          </defs>

          {/* 流量路径 - 从路由器到稳定版本 */}
          <g className="stable-path">
            <path 
              d="M210,55 Q240,30 300,30" 
              stroke="#1890FF" 
              strokeWidth={1.5 + (100 - trafficPercentage) / 30} 
              strokeDasharray="3,2" 
              fill="none"
              markerEnd="url(#arrowStable)"
            />
          </g>

          {/* 流量路径 - 从路由器到金丝雀版本 - 仅在阶段1及以后显示 */}
          {stage >= 1 && (
            <g className="canary-path animated-path">
              <path 
                d="M210,85 Q240,110 300,110" 
                stroke="#FA8C16" 
                strokeWidth={1.5 + trafficPercentage / 30} 
                strokeDasharray="3,2" 
                fill="none"
                markerEnd="url(#arrowCanary)"
              />
            </g>
          )}
        </g>

        {/* 监控状态 - 仅在阶段1及以后显示 */}
        {stage >= 1 && (
          <g 
            className="monitoring-panel"
            transform="translate(630, 120)"
          >
            <rect 
              x="-55" 
              y="-40" 
              width="110" 
              height={stage >= 3 ? "80" : "60"} 
              rx="4" 
              fill="#F6FFED" 
              stroke="#B7EB8F" 
              strokeWidth="1"
              className={`monitoring-box stage-${stage}`}
            />
            <text x="0" y="-25" textAnchor="middle" fontSize="10" fontWeight="bold" fill="#52C41A">监控状态</text>
            <line x1="-45" y1="-15" x2="45" y2="-15" stroke="#D9F7BE" strokeWidth="1" />

            {/* 阶段1：初始监控数据 */}
            {stage >= 1 && (
              <g className="metrics">
                <text x="-40" y="-5" textAnchor="start" fontSize="8" fill="#389E0D">• CPU 负载正常</text>
                <text x="-40" y="10" textAnchor="start" fontSize="8" fill="#389E0D">• 内存占用正常</text>
                <text x="-40" y="25" textAnchor="start" fontSize="8" fill="#389E0D">• 响应时间: 230ms</text>
              </g>
            )}

            {/* 阶段3：完成确认信息 */}
            {stage >= 3 && (
              <g className="completion-status animated-element delay-1">
                <line x1="-45" y1="35" x2="45" y2="35" stroke="#D9F7BE" strokeWidth="1" />
                <text x="0" y="45" textAnchor="middle" fontSize="9" fontWeight="bold" fill="#389E0D">
                  金丝雀部署成功
                </text>
              </g>
            )}
          </g>
        )}

        {/* 阶段说明 */}
        <g className="stage-description" transform="translate(630, 55)">
          <rect 
            x="-55" 
            y="-15" 
            width="110" 
            height="25" 
            rx="4" 
            fill={stage >= 1 ? "#FFF7E6" : "#F5F5F5"} 
            stroke={stage >= 1 ? "#FFD591" : "#D9D9D9"} 
            strokeWidth="1"
          />
          <text x="0" y="5" textAnchor="middle" fontSize="10" fontWeight="bold" fill={stage >= 1 ? "#FA8C16" : "#8C8C8C"}>
            {stage === 0 && "准备部署"}
            {stage === 1 && "阶段1: 20% 流量"}
            {stage === 2 && "阶段2: 50% 流量"}
            {stage === 3 && "阶段3: 100% 流量"}
          </text>
        </g>
      </svg>
    </div>
  );
};

export default CanaryDeploymentDiagram; 