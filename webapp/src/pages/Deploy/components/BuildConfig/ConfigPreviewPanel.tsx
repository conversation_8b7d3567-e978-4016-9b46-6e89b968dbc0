import React from 'react';
import { 
  <PERSON>, Typography, <PERSON>, Button, Alert, 
  Divider, Tag, List, Tooltip
} from 'antd';
import { 
  CopyOutlined, CheckCircleOutlined, 
  ExclamationCircleOutlined, InfoCircleOutlined,
  ToolOutlined, RocketOutlined, DatabaseOutlined
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface ConfigPreviewPanelProps {
  formValues: any;
  selectedLanguage: string;
  previewCommand: string;
}

export const ConfigPreviewPanel: React.FC<ConfigPreviewPanelProps> = ({
  formValues,
  selectedLanguage,
  previewCommand
}) => {
  
  // 生成构建命令预览
  const generateBuildCommand = () => {
    if (!formValues) return '';
    
    const buildParams = formValues.build_params || {};
    const runtimeParams = formValues.runtime_params || {};
    
    if (selectedLanguage === 'java') {
      let command = '';
      
      // Maven构建命令
      if (buildParams.build_tool === 'maven' || buildParams.build_tool === 'auto') {
        command = 'mvn clean package';
        
        if (buildParams.skip_tests) {
          command += ' -DskipTests';
        }
        
        if (buildParams.parallel_build && buildParams.parallel_threads) {
          command += ` -T ${buildParams.parallel_threads}`;
        }
        
        if (buildParams.maven_profiles) {
          command += ` -P${buildParams.maven_profiles}`;
        }
        
        if (buildParams.test_params) {
          command += ` ${buildParams.test_params}`;
        }
      }
      
      return command;
    }
    
    return '';
  };

  // 生成Docker运行命令
  const generateDockerCommand = () => {
    if (!formValues) return '';
    
    const containerParams = formValues.container_params || {};
    const runtimeParams = formValues.runtime_params || {};
    
    let command = 'docker run -d';
    
    // 端口映射
    if (containerParams.port) {
      command += ` \\\n  -p ${containerParams.port}:${containerParams.port}`;
    }
    
    // 环境变量
    if (runtimeParams.environment_variables) {
      const envVars = runtimeParams.environment_variables.split('\n').filter(line => line.trim());
      envVars.forEach(env => {
        if (env.includes('=')) {
          command += ` \\\n  -e ${env}`;
        }
      });
    }
    
    // 内存限制
    if (runtimeParams.jvm_memory && !runtimeParams.jvm_memory.includes('custom:')) {
      command += ` \\\n  --memory=${runtimeParams.jvm_memory}`;
    }
    
    // CPU限制
    command += ` \\\n  --cpus=1.0`;
    
    // 镜像名称
    command += ` \\\n  ${formValues.app_name || 'my-app'}:latest`;
    
    return command;
  };

  // 生成配置摘要
  const generateConfigSummary = () => {
    if (!formValues) return [];
    
    const buildParams = formValues.build_params || {};
    const runtimeParams = formValues.runtime_params || {};
    const containerParams = formValues.container_params || {};
    
    const summary = [];
    
    // 语言和版本
    if (selectedLanguage === 'java' && buildParams.java_version) {
      summary.push(`语言: Java ${buildParams.java_version}`);
    } else if (selectedLanguage && buildParams[`${selectedLanguage}_version`]) {
      summary.push(`语言: ${selectedLanguage.toUpperCase()} ${buildParams[`${selectedLanguage}_version`]}`);
    }
    
    // 构建工具
    if (buildParams.build_tool) {
      summary.push(`构建工具: ${buildParams.build_tool === 'auto' ? '自动检测' : buildParams.build_tool}`);
    }
    
    // 内存配置
    if (runtimeParams.jvm_memory) {
      const memory = runtimeParams.jvm_memory.includes('custom:') 
        ? '自定义配置' 
        : runtimeParams.jvm_memory;
      summary.push(`内存配置: ${memory}`);
    }
    
    // GC策略
    if (runtimeParams.gc_type && selectedLanguage === 'java') {
      summary.push(`GC策略: ${runtimeParams.gc_type}`);
    }
    
    // 端口配置
    if (containerParams.port) {
      summary.push(`端口映射: ${containerParams.port}:${containerParams.port}`);
    }
    
    // 环境配置
    if (buildParams.maven_profiles) {
      summary.push(`环境: ${buildParams.maven_profiles}`);
    }
    
    // 测试配置
    if (buildParams.skip_tests) {
      const testInfo = buildParams.test_params ? `跳过 (指定${buildParams.test_params})` : '跳过';
      summary.push(`测试: ${testInfo}`);
    }
    
    // 并行构建
    if (buildParams.parallel_build && buildParams.parallel_threads) {
      summary.push(`并行: ${buildParams.parallel_threads}线程构建`);
    }
    
    return summary;
  };

  // 生成验证状态
  const generateValidationStatus = () => {
    if (!formValues) return [];
    
    const validations = [];
    const buildParams = formValues.build_params || {};
    const runtimeParams = formValues.runtime_params || {};
    const containerParams = formValues.container_params || {};
    
    // 基础配置验证
    if (formValues.language) {
      validations.push({ type: 'success', message: '基础配置完成' });
    }
    
    // 构建参数验证
    if (selectedLanguage === 'java' && buildParams.java_version) {
      validations.push({ type: 'success', message: 'Java版本兼容' });
    }
    
    if (buildParams.build_tool) {
      validations.push({ type: 'success', message: '构建工具配置有效' });
    }
    
    // 编译参数验证
    if (buildParams.compiler_options) {
      validations.push({ type: 'success', message: '编译参数正确' });
    }
    
    // 内存配置验证
    if (runtimeParams.jvm_memory) {
      validations.push({ type: 'success', message: '内存配置合理' });
    }
    
    // 警告和建议
    if (!runtimeParams.health_check_enabled) {
      validations.push({ type: 'warning', message: '建议添加健康检查' });
    }
    
    if (formValues.custom_build_script && formValues.custom_build_script.length > 200) {
      validations.push({ type: 'warning', message: '构建脚本较复杂' });
    }
    
    // 可选配置
    if (!runtimeParams.jvm_options) {
      validations.push({ type: 'info', message: 'JVM参数可选配置' });
    }
    
    return validations;
  };

  const buildCommand = generateBuildCommand();
  const dockerCommand = generateDockerCommand();
  const configSummary = generateConfigSummary();
  const validationStatus = generateValidationStatus();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="config-preview-panel">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 构建命令预览 */}
        {buildCommand && (
          <Card 
            title={
              <Space>
                <ToolOutlined />
                <Text strong>构建命令预览</Text>
              </Space>
            }
            size="small"
            extra={
              <Tooltip title="复制命令">
                <Button 
                  type="text" 
                  icon={<CopyOutlined />} 
                  size="small"
                  onClick={() => copyToClipboard(buildCommand)}
                />
              </Tooltip>
            }
          >
            <Paragraph 
              code 
              style={{ 
                margin: 0, 
                fontSize: '12px',
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {buildCommand}
            </Paragraph>
          </Card>
        )}

        {/* Java运行时命令预览 */}
        {previewCommand && selectedLanguage === 'java' && (
          <Card 
            title={
              <Space>
                <RocketOutlined />
                <Text strong>运行时命令</Text>
              </Space>
            }
            size="small"
            extra={
              <Tooltip title="复制命令">
                <Button 
                  type="text" 
                  icon={<CopyOutlined />} 
                  size="small"
                  onClick={() => copyToClipboard(previewCommand)}
                />
              </Tooltip>
            }
          >
            <Paragraph 
              code 
              style={{ 
                margin: 0, 
                fontSize: '12px',
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {previewCommand}
            </Paragraph>
          </Card>
        )}

        {/* Docker运行命令 */}
        {dockerCommand && (
          <Card 
            title={
              <Space>
                <DatabaseOutlined />
                <Text strong>Docker运行命令</Text>
              </Space>
            }
            size="small"
            extra={
              <Tooltip title="复制命令">
                <Button 
                  type="text" 
                  icon={<CopyOutlined />} 
                  size="small"
                  onClick={() => copyToClipboard(dockerCommand)}
                />
              </Tooltip>
            }
          >
            <Paragraph 
              code 
              style={{ 
                margin: 0, 
                fontSize: '12px',
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {dockerCommand}
            </Paragraph>
          </Card>
        )}

        {/* 配置摘要 */}
        <Card 
          title={
            <Space>
              <InfoCircleOutlined />
              <Text strong>配置摘要</Text>
            </Space>
          }
          size="small"
        >
          <List
            size="small"
            dataSource={configSummary}
            renderItem={(item) => (
              <List.Item style={{ padding: '4px 0', border: 'none' }}>
                <Text style={{ fontSize: '12px' }}>• {item}</Text>
              </List.Item>
            )}
          />
        </Card>

        {/* 验证状态 */}
        <Card 
          title={
            <Space>
              <CheckCircleOutlined />
              <Text strong>验证状态</Text>
            </Space>
          }
          size="small"
        >
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            {validationStatus.map((validation, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'center' }}>
                {validation.type === 'success' && (
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                )}
                {validation.type === 'warning' && (
                  <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
                )}
                {validation.type === 'info' && (
                  <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                )}
                <Text style={{ fontSize: '12px' }}>{validation.message}</Text>
              </div>
            ))}
          </Space>
        </Card>
      </Space>
    </div>
  );
};
