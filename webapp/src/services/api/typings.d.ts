declare namespace API {
  interface PageInfo {
    current?: number;
    pageSize?: number;
    total?: number;
    list?: Array<Record<string, any>>;
  }

  interface PageInfo_UserInfo_ {
    current?: number;
    pageSize?: number;
    total?: number;
    list?: Array<UserInfo>;
  }

  interface PageInfo_ImageInfo_ {
    current?: number;
    pageSize?: number;
    total?: number;
    list?: Array<ImageInfo>;
  }

  interface PageInfo_ComponentInfo_ {
    current?: number;
    pageSize?: number;
    total?: number;
    list?: Array<ComponentInfo>;
  }

  interface Result {
    success?: boolean;
    errorMessage?: string;
    data?: Record<string, any>;
  }

  interface Result_PageInfo_UserInfo__ {
    success?: boolean;
    errorMessage?: string;
    data?: PageInfo_UserInfo_;
  }

  interface Result_UserInfo_ {
    success?: boolean;
    errorMessage?: string;
    data?: UserInfo;
  }

  interface Result_string_ {
    success?: boolean;
    errorMessage?: string;
    data?: string;
  }

  interface Result_PageInfo_ImageInfo__ {
    success?: boolean;
    errorMessage?: string;
    data?: PageInfo_ImageInfo_;
  }

  interface Result_ImageInfo_ {
    success?: boolean;
    errorMessage?: string;
    data?: ImageInfo;
  }

  interface Result_PageInfo_ComponentInfo__ {
    success?: boolean;
    errorMessage?: string;
    data?: PageInfo_ComponentInfo_;
  }

  interface Result_ComponentInfo_ {
    success?: boolean;
    errorMessage?: string;
    data?: ComponentInfo;
  }


  interface UserInfo {
    id?: string;
    name?: string;
    nickName?: string;
    email?: string;
  }

  interface UserInfoVO {
    name?: string;
    nickName?: string;
    email?: string;
  }

  type ImageTypeEnum = 'BASE' | 'CUSTOM' | 'APPLICATION';

  interface ImageInfo {
    id?: string;
    name?: string;
    url?: string;
    tag?: string;
    type?: ImageTypeEnum;
    description?: string;
    createdAt?: string;
    updatedAt?: string;
  }

  // 镜像请求/响应对象
  interface ImageInfoVO {
    name?: string;
    url?: string;
    tag?: string;
    type?: ImageTypeEnum;
    description?: string;
  }

  // 组件类型枚举
  type ComponentTypeEnum = 'JAR' | 'WAR' | 'ZIP' | 'CONFIG' | 'OTHER';

  // 组件信息
  interface ComponentInfo {
    id?: string;
    name?: string;
    version?: string;
    type?: ComponentTypeEnum;
    url?: string;
    description?: string;
    createdAt?: string;
    updatedAt?: string;
  }

  interface ComponentInfoVO {
    name?: string;
    version?: string;
    type?: ComponentTypeEnum;
    url?: string;
    description?: string;
  }


  export type CurrentUser = {
    avatar?: string;
    name?: string;
    title?: string;
    group?: string;
    signature?: string;
    tags?: {
      key: string;
      label: string;
    }[];
    userid?: string;
    access?: string;
    unreadCount?: number;
  };

  // 运行时配置
  export interface RuntimeConfig {
    replicas: number;
    cpu: string;
    memory: string;
    baseImage?: string;
    namespace?: string;
    requestCpu?: string;
    requestMemory?: string;
    cpuLimit?: string;
    timeout?: number;
  }

  // 应用配置
  export interface AppConfig {
    runtime: RuntimeConfig;
    configMode: string;
  }

  export interface EnvironmentVariable {
    id: number;
    app_id: number; 
    env_id: number;
    key_name: string;
    value: string;
    description: string;
    c_t: number;
    create_by: number;
    u_t: number;
    update_by: number;
    status: number;
    // 变量类型，0-普通变量，1-可编程变量
    variable_type?: number;
    // 依赖的其他环境变量ID列表
    dependencies?: string;
  }

  export interface UpdateEnvironmentVariableRequest {
    id: number;
    key_name: string;
    value: string;
    description: string;
    variable_type: number;
    status: number;
  }

  export interface CreateEnvironmentVariableRequest {
    app_id: number;
    env_id: number;
    key_name: string;
    value: string;
    description: string;
    variable_type: number;
    dependencies: string;
  }

  export interface AppData {
    application: Application;
    app_settings: AppSettings;
    config_files: ConfigFile[];
  }
  
  export interface Group {
    id: number;
    code: string;
    name: string;
    description: string;
    app_id: number;
    env_id: number;
    is_branch: number
    semver: string
    commit_id: string
    status: number
    c_t: number;                  // 创建时间
    create_by: number;            // 创建人ID
    u_t: number;                  // 更新时间
    update_by: number;            // 更新人ID
    status: number;               // 状态 0:禁用 1:启用
    is_deleted: number;           // 记录状态：0-正常 1-删除
  }
  
  export interface Application {
    id: number;
    code: string;
    name: string;
    language: string;
    basic: number;
    sequence: number;
    app_type_id: number;
    app_type: AppType;
    level: number;
    corp: string;
    owt: string;
    pdl: string;
    sg: string;
    srv: string;
    usn: string;
    create_time: string;
    create_by: number;
    update_time: string;
    update_by: number;
    status: number;
    srvtree_id: number;
  }

  export interface AppType {
    id: number;
    code: string;
    name: string;
    create_time: string;
    create_by: number;
    update_time: string;
    update_by: number;
    status: number;
  }

  export interface ConfigFile {
    id: number;
    app_id: number;
    env_id: number;
    group_id: number;
    name: string;
    path: string;
    format: string;
    description: string;
    current_version?: number;
    c_t?: number;
    create_by?: number;
    u_t?: number;
    update_by?: number;
    status?: number;
    content_payload?: ConfigFileContent;
  }

  export interface ConfigFileContent {
    id: number;
    file_id: number;
    content: string;
    version: number;
    md5: string;
    size: number;
    c_t: number;
    create_by: number;
    remark: string;
    u_t: number;
    update_by: number;
    status: number;
  }

  
  export interface AppSettings {
    id: number;
    app_id: number;
    code_repository: string;
    repo_project_id: number;
    build_package_name: string;
    deploy_package_name: string;
    start_command: string;
    stop_command: string;
    health_check_command: string;
    health_check: number;
    request_path: string;
    request_port: number;
    response_status: number;
    max_retries: number;
    request_timeout: number;
    rate: number;
    deploy_path: string;
    log_path: string;
    agent: string;
    sdk_version: string;
    base_image: string;
    probe: string;
    route_type: string;
    strategy: string;
    replicas?: number;
    request_cpu?: string;
    request_memory?: string;
    limit_cpu?: string;
    limit_memory?: string;
    svc_port?: number;
    svc_address?: string;
    pod_port?: number;
    create_time: string;
    create_by: number;
    update_time: string;
    update_by: number;
    status: number;
  }

  // 服务树节点类型定义
  export interface OpsNode {
    id: number;
    label: string;
    parentId: number;
    name: string;
    code: string;
    nodeCode: string;
    type: string;
    tagStr: string;
    hasLeaf: boolean;
    children?: OpsNode[];
  }
  
  // 获取应用信息请求类型
  export interface  GetAppInfoRequest {
    nodeId: number;
    envId: number;
  }
  
  // 后端标准响应格式
  export interface StandardResponse<T> {
    code: number = 0;
    message: string = "success";
    data: T;
  }

  export interface CreateDeployTaskRequest {
    app_id: number;
    env_id: number;
    is_branch: number;
    group_ids: number[];
    semver: string;
    commit_id: string;
    deploy_type: DeployTaskType;
    description: string;
  }

  // 部署任务主表类型定义（
  export interface DeployTask {
    id: number;                // 主键ID
    description: string;       // 任务描述
    app_id: number;            // 应用ID
    env_id: number;            // 环境ID
    status: number;            // 状态
    task_count: number;        // 任务数量
    task_success: number;      // 成功任务数量
    task_failed: number;       // 失败任务数量
    semver: string;           // 版本号
    commit_id: string;         // 提交ID
    rdm_id: string;            // RDM ID
    created_by: number;        // 创建人
    updated_by: number;        // 最后更新人
    c_t: number;               // 创建时间（时间戳）
    u_t: number;               // 更新时间（时间戳）
    is_deleted: number;        // 记录状态：0-正常 1-删除
    // 关联关系
    task_details?: DeployTaskDetail[]; // 任务详情列表（可选）
  }

  // 部署任务详情表类型定义
  export interface DeployTaskDetail {
    id: number;
    app_id: number;
    env_id: number;
    group_id: number;
    task_id: number;
    version: string;
    commit_id: string;
    status: number;
    start_time: number;
    end_time: number;
    duration: number;
    current_step: string;
    total_steps: number;
    current_step_index: number;
    progress: number;
    estimated_end_time: number;
    needs_confirmation: number;
    config: string;
    deploy_type: string;
    deploy_strategy: string;
    c_t: number;
    u_t: number;
    is_deleted: number;
    // 关联关系
    task?: DeployTask;
    logs?: DeployLog[];
  }

  // 部署日志表类型定义（与后端 model.DeployLog 字段对应）
  export interface DeployLog {
    id: number;
    deploy_task_detail_id: number;
    log_type: string;
    content: string;
    timestamp: number;
    step: string;
    step_index: number;
    c_t: number;
    create_by: number;
    u_t: number;
    update_by: number;
    is_deleted: number;
    // 关联关系
    task_detail?: DeployTaskDetail;
  }

  // 分页查询部署任务参数
  export interface QueryDeployTasksParams {
    app_id: number;
    env_id: number;
    page?: number;
    page_size?: number;
    status?: number;
    deploy_type?: string;
    deploy_strategy?: string;
    group_id?: number;
    keyword?: string;
  }
  
  // 部署任务分页响应
  export interface DeployTasksResponse {
    page_info: {
      page: number;
      page_size: number;
      total: number;
    };
    list: DeployTask[];
  }

  export interface CreateGroupRequest {
    code?: string;
    name: string;
    description?: string;
    app_id: number;
    env_id: number;
    status?: number;              // 状态 0:禁用 1:启用
  }

  // 容器配置表类型定义
  export interface Container {
    id: number;
    app_id: number;
    env_id: number;
    group_id: number;
    base_image: string;
    replicas: number;
    namespace: string;
    ports: PortConfig[];
    resources: ResourceConfig;
    environment: Record<string, string>;
    strategy: string;
    rollout_config: RolloutConfig;
    status: number;
    // 部署策略配置
    bluegreen_config?: {
      auto_promotion_enabled: boolean;
      scale_down_delay_seconds: number;
      preview_replica_count: number;
    };
    canary_config?: {
      steps: {
        set_weight: number;
        pause_duration: number;
      }[];
      traffic_routing: {
        istio: {
          virtual_service: {
            name: string;
            routes: string[];
          }
        }
      }
    };
    // 审计字段
    c_t: number;
    create_by: number;
    u_t: number;
    update_by: number;
    is_deleted: number;
  }

  // 端口配置
  export interface PortConfig {
    name: string;
    container_port: number;
    service_port: number;
    protocol: string;
  }

  // 资源配置
  export interface ResourceConfig {
    requests: ResourceSpec;
    limits: ResourceSpec;
  }

  export interface ResourceSpec {
    cpu: string;
    memory: string;
  }

  // 发布配置
  export interface RolloutConfig {
    max_surge: string;
    max_unavailable: string;
    revision_history_limit?: number;
    timeout_seconds?: number;
  }


  // 运行时设置类型定义
  export interface RuntimeSetting {
    id: number;
    app_id: number;
    env_id: number;
    group_id: number;
    // 资源配置
    replicas: number;
    cpu: string;
    memory: string;
    request_cpu: string;
    request_memory: string;
    namespace: string;
    image_id: number;
    // 目录配置
    work_dir: string;
    log_dir: string;
    // 命令配置
    start_command: string;
    stop_command: string;
    // 部署配置
    priority: number;
    // 审计字段
    c_t: number;
    create_by: number;
    u_t: number;
    update_by: number;
    status: number;
    // 关联字段
    image_name?: string;
  }
  
  // 运行时探针定义
  export interface RuntimeProbe {
    id: number;
    app_id: number;
    env_id: number;
    probe_type: number; // 1: 启动探针, 2: 就绪探针, 3: 存活探针
    probe_method: number; // 1: HTTP, 2: TCP, 3: 命令行
    // 通用配置
    initial_delay_seconds: number;
    timeout_seconds: number;
    period_seconds: number;
    success_threshold: number;
    failure_threshold: number;
    // HTTP探针配置
    http_path?: string;
    http_port?: number;
    http_scheme?: string;
    http_headers?: Record<string, string>;
    // TCP探针配置
    tcp_port?: number;
    // 命令行探针配置
    exec_command?: string;
    // 审计字段
    c_t: number;
    create_by: number;
    u_t: number;
    update_by: number;
    status: number;
  }

  // 部署历史记录
  export interface DeployHistory {
    id: number;
    app_id: number;
    env_id: number;
    version: string;
    description: string;
    deploy_type: string;
    deploy_strategy: string;
    status: string;
    operator: string;
    start_time: number;
    end_time: number;
    duration: number;
    target_groups: string[];
    is_multi_group: boolean;
    is_child: boolean;
  }

  // GitLab分支信息
  export interface GitLabBranch {
    name: string;
    commit: {
      id: string;
      short_id: string;
      title: string;
      author_name: string;
      author_email: string;
      authored_date: string;
      committer_name: string;
      committer_email: string;
      committed_date: string;
      created_at: string;
      message: string;
      parent_ids: string[] | null;
      stats: any | null;
      status: any | null;
      last_pipeline: any | null;
      project_id: number;
      trailers: any | null;
      extended_trailers: any | null;
      web_url: string;
    };
    protected: boolean;
    merged: boolean;
    default: boolean;
    can_push: boolean;
    developers_can_push: boolean;
    developers_can_merge: boolean;
    web_url: string;
  }

  // GitLab Tag信息
  export interface GitLabTag {
    name: string;
    commit: {
      id: string;
      short_id: string;
      title: string;
      author_name: string;
      author_email: string;
      authored_date: string;
      committer_name: string;
      committer_email: string;
      committed_date: string;
      created_at: string;
      message: string;
      parent_ids: string[] | null;
      stats: any | null;
      status: any | null;
      last_pipeline: any | null;
      project_id: number;
      trailers: any | null;
      extended_trailers: any | null;
      web_url: string;
    };
    protected: boolean;
    release: {
      tag_name: string;
      description: string;
    } | null;
    commit_path: string;
    ref_path: string;
    web_url: string;
  }

  // Semver列表项
  export interface SemverItem {
    name: string;
    commit_id: string;
    commit_message?: string;
    author_name?: string;
    committed_date?: string;
    web_url?: string;
  }

  // Semver列表响应
  export interface SemverListResponse {
    code: number;
    data: SemverItem[];
    message: string;
  }

  // 部署历史列表响应
  export interface DeployHistoryResponse {
    list: DeployHistory[];
    total: number;
    page: number;
    page_size: number;
  }

  // ================================
  // Pod 相关类型定义
  // ================================

  // Pod基础类型
  interface Pod {
    id: string;
    name: string;
    namespace: string;
    status: string;
    restarts: number;
    age: string;
    ready: string;
    ip: string;
    node: string;
    cpu: string;
    memory: string;
    create_time: string;
    labels: { [key: string]: string };
    annotations: { [key: string]: string };
  }

  interface PodListResponse {
    pods: Pod[];
    total: number;
  }

  interface ArthasStartRequest {
    namespace: string;
    pod_name: string;
    container_name?: string;
    port?: number;
  }

  interface ArthasStatusResponse {
    pod_name: string;
    namespace: string;
    status: string;
    java_pid: string;
    http_port: number;
    telnet_port: number;
    web_console_url: string;
    start_time: string;
    last_active_time: string;
    error_message?: string;
  }

  interface RestartRequest {
    namespace: string;
    pod_names: string[];
    strategy: 'graceful' | 'force';
    config?: {
      timeout: number;
      max_concurrent: number;
    };
  }

  interface RestartResult {
    total_pods: number;
    successful_pods: number;
    failed_pods: string[];
    message: string;
    errors?: { [podName: string]: string };
  }

  // Result类型定义
  interface Result_PodListResponse_ {
    success?: boolean;
    errorMessage?: string;
    data?: PodListResponse;
  }

  interface Result_Pod_ {
    success?: boolean;
    errorMessage?: string;
    data?: Pod;
  }

  interface Result_RestartResult_ {
    success?: boolean;
    errorMessage?: string;
    data?: RestartResult;
  }

  interface Result_ArthasStatusResponse_ {
    success?: boolean;
    errorMessage?: string;
    data?: ArthasStatusResponse;
  }
}
