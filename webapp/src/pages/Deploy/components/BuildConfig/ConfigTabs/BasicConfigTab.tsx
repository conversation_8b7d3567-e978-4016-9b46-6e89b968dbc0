import React from 'react';
import { 
  Form, Input, Select, Row, Col, 
  Typography, Tooltip, Space
} from 'antd';
import { 
  CodeOutlined, QuestionCircleOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

interface BasicConfigTabProps {
  form: any;
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
  onTemplateChange: (template: string) => void;
}

export const BasicConfigTab: React.FC<BasicConfigTabProps> = ({
  form,
  selectedLanguage = 'java', // 添加默认值
  onLanguageChange,
  onTemplateChange
}) => {
  return (
    <div className="tab-content">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 编程语言选择 */}
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label={
                <Space>
                  <span>编程语言</span>
                  <Tooltip title="选择应用的编程语言">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
              name="language"
              rules={[{ required: true, message: '请选择编程语言' }]}
            >
              <Select 
                onChange={onLanguageChange}
                placeholder="选择编程语言"
                size="large"
              >
                <Option value="java">Java</Option>
                <Option value="go">Go</Option>
                <Option value="nodejs">Node.js</Option>
                <Option value="python">Python</Option>
                <Option value="php">PHP</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              label={
                <Space>
                  <span>应用类型</span>
                  <Tooltip title="选择应用的类型">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
              name="app_type"
            >
              <Select placeholder="选择应用类型" size="large">
                <Option value="web">Web应用</Option>
                <Option value="api">API服务</Option>
                <Option value="microservice">微服务</Option>
                <Option value="batch">批处理</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item 
              label={
                <Space>
                  <span>主端口</span>
                  <Tooltip title="应用暴露的主要端口">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
              name={['container_params', 'port']}
            >
              <Input 
                placeholder="8080" 
                size="large"
                style={{ width: '100%' }} 
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 构建模板和基础镜像 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <Space>
                  <span>构建模板</span>
                  <Tooltip title="选择适合您应用的构建模板">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
              name="template_type"
              rules={[{ required: true, message: '请选择构建模板' }]}
            >
              <Select 
                onChange={onTemplateChange}
                placeholder="选择构建模板"
                size="large"
              >
                {selectedLanguage === 'java' && (
                  <>
                    <Option value="smart-detect">智能检测</Option>
                    <Option value="spring-boot-jar">Spring Boot JAR</Option>
                    <Option value="spring-boot-war">Spring Boot WAR</Option>
                    <Option value="tomcat-war">Tomcat WAR</Option>
                  </>
                )}
                {selectedLanguage === 'go' && (
                  <>
                    <Option value="gin-api">Gin API</Option>
                    <Option value="echo-api">Echo API</Option>
                    <Option value="standard-go">标准Go应用</Option>
                  </>
                )}
                {selectedLanguage === 'nodejs' && (
                  <>
                    <Option value="express-api">Express API</Option>
                    <Option value="nest-api">NestJS API</Option>
                    <Option value="next-app">Next.js应用</Option>
                  </>
                )}
                {selectedLanguage === 'python' && (
                  <>
                    <Option value="flask-api">Flask API</Option>
                    <Option value="django-app">Django应用</Option>
                    <Option value="fastapi">FastAPI</Option>
                  </>
                )}
                {selectedLanguage === 'php' && (
                  <>
                    <Option value="laravel">Laravel</Option>
                    <Option value="symfony">Symfony</Option>
                    <Option value="wordpress">WordPress</Option>
                  </>
                )}
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              label={
                <Space>
                  <span>基础镜像</span>
                  <Tooltip title="Docker基础镜像">
                    <QuestionCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              }
              name={['container_params', 'base_image']}
            >
              <Select placeholder="选择基础镜像" size="large">
                {selectedLanguage === 'java' && (
                  <>
                    <Option value="openjdk:17-jre-slim">openjdk:17-jre-slim</Option>
                    <Option value="openjdk:11-jre-slim">openjdk:11-jre-slim</Option>
                    <Option value="openjdk:8-jre-slim">openjdk:8-jre-slim</Option>
                    <Option value="eclipse-temurin:17-jre">eclipse-temurin:17-jre</Option>
                  </>
                )}
                {selectedLanguage === 'nodejs' && (
                  <>
                    <Option value="node:18-alpine">node:18-alpine</Option>
                    <Option value="node:16-alpine">node:16-alpine</Option>
                    <Option value="node:20-alpine">node:20-alpine</Option>
                  </>
                )}
                {selectedLanguage === 'python' && (
                  <>
                    <Option value="python:3.11-slim">python:3.11-slim</Option>
                    <Option value="python:3.10-slim">python:3.10-slim</Option>
                    <Option value="python:3.9-slim">python:3.9-slim</Option>
                  </>
                )}
                {selectedLanguage === 'go' && (
                  <>
                    <Option value="golang:1.21-alpine">golang:1.21-alpine</Option>
                    <Option value="golang:1.20-alpine">golang:1.20-alpine</Option>
                    <Option value="alpine:latest">alpine:latest</Option>
                  </>
                )}
                {selectedLanguage === 'php' && (
                  <>
                    <Option value="php:8.2-fpm-alpine">php:8.2-fpm-alpine</Option>
                    <Option value="php:8.1-fpm-alpine">php:8.1-fpm-alpine</Option>
                    <Option value="nginx:alpine">nginx:alpine</Option>
                  </>
                )}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 工作目录和应用名称 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="工作目录"
              name="work_dir"
            >
              <Input 
                placeholder="/app" 
                size="large"
              />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              label="应用名称"
              name="app_name"
            >
              <Input 
                placeholder="my-application" 
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>
      </Space>
    </div>
  );
};
