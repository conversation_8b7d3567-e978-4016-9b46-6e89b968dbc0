import React from 'react';
import { Descriptions, Card, Typography, Tag, Space, Tooltip, Button } from 'antd';
import { 
  CheckCircleOutlined, 
  ThunderboltOutlined, 
  ClockCircleOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  EditOutlined,
  SyncOutlined
} from '@ant-design/icons';

const { Text } = Typography;

export interface ProbeConfig {
  // 启动探针
  startupProbePath?: string;
  startupProbePort?: number;
  startupInitialDelaySeconds?: number;
  startupTimeoutSeconds?: number;
  startupPeriodSeconds?: number;
  startupFailureThreshold?: number;
  startupSuccessThreshold?: number;
  startupProbeEnabled?: boolean;
  // 存活探针
  livenessProbePath?: string;
  livenessProbePort?: number;
  livenessInitialDelaySeconds?: number;
  livenessTimeoutSeconds?: number;
  livenessPeriodSeconds?: number;
  livenessFailureThreshold?: number;
  livenessSuccessThreshold?: number;
  livenessProbeEnabled?: boolean;
  // 就绪探针
  readinessProbePath?: string;
  readinessProbePort?: number;
  readinessInitialDelaySeconds?: number;
  readinessTimeoutSeconds?: number;
  readinessPeriodSeconds?: number;
  readinessFailureThreshold?: number;
  readinessSuccessThreshold?: number;
  readinessProbeEnabled?: boolean;
}

export interface ProbeTooltipProps {
  probeConfig?: ProbeConfig;
  onEdit?: () => void;
  editable?: boolean;
}

// 帮助提示内容
const ProbeHelpInfo = {
  startup: (
    <div>
      <p>启动探针用于检测应用是否已启动完成。</p>
      <p>应用启动期间存活探针会被延迟执行，避免应用启动过程中被误杀。</p>
      <p>适用于启动较慢的应用。</p>
    </div>
  ),
  liveness: (
    <div>
      <p>存活探针用于检测应用是否存活。</p>
      <p>当存活探针失败时，Kubernetes 会重启容器。</p>
      <p>适用于检测应用是否处于异常状态需要重启。</p>
    </div>
  ),
  readiness: (
    <div>
      <p>就绪探针用于检测应用是否准备好接收流量。</p>
      <p>当就绪探针失败时，Kubernetes 会将容器从服务端点中移除。</p>
      <p>适用于检测应用是否能正常处理请求。</p>
    </div>
  )
};

// 探针类型配色方案
const probeColorScheme = {
  startup: {
    color: '#fa8c16',
    icon: <ThunderboltOutlined />,
    bgColor: '#fffaf3',
  },
  liveness: {
    color: '#1890ff',
    icon: <ClockCircleOutlined />,
    bgColor: '#f0f7ff',
  },
  readiness: {
    color: '#52c41a',
    icon: <CheckCircleOutlined />,
    bgColor: '#f9fff6',
  }
};

const ProbeTooltip: React.FC<ProbeTooltipProps> = ({
  probeConfig = {
    // 启动探针默认值
    startupProbePath: '/health/startupProbe',
    startupProbePort: 8089,
    startupInitialDelaySeconds: 60,
    startupTimeoutSeconds: 3,
    startupPeriodSeconds: 10,
    startupFailureThreshold: 100,
    startupSuccessThreshold: 1,
    startupProbeEnabled: true,
    // 存活探针默认值
    livenessProbePath: '/actuator/health/liveness',
    livenessProbePort: 7279,
    livenessInitialDelaySeconds: 30,
    livenessTimeoutSeconds: 5,
    livenessPeriodSeconds: 10,
    livenessFailureThreshold: 3,
    livenessSuccessThreshold: 1,
    livenessProbeEnabled: true,
    // 就绪探针默认值
    readinessProbePath: '/actuator/health/readiness',
    readinessProbePort: 7279,
    readinessInitialDelaySeconds: 20,
    readinessTimeoutSeconds: 5,
    readinessPeriodSeconds: 5,
    readinessFailureThreshold: 3,
    readinessSuccessThreshold: 1,
    readinessProbeEnabled: true,
  },
  onEdit,
  editable = false
}) => {
  const renderProbeEndpoint = (path?: string, port?: number) => {
    if (!path) return <Text type="secondary">未配置</Text>;
    return (
      <Text code>
        http://[服务]:{port || 80}{path}
      </Text>
    );
  };
  
  const renderProbeCard = (
    type: 'startup' | 'liveness' | 'readiness',
    path?: string,
    port?: number,
    initialDelay?: number,
    timeout?: number,
    period?: number,
    failureThreshold?: number,
    successThreshold?: number
  ) => {
    const { color, icon, bgColor } = probeColorScheme[type];
    const title = type === 'startup' ? '启动探针' : 
                  type === 'liveness' ? '存活探针' : '就绪探针';
    const englishName = type === 'startup' ? 'Startup Probe' : 
                        type === 'liveness' ? 'Liveness Probe' : 'Readiness Probe';
                        
    return (
      <div style={{ 
        padding: '10px', 
        backgroundColor: bgColor, 
        borderRadius: '6px',
        border: `1px solid ${color}15`,
        marginBottom: '10px',
        boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between', 
          marginBottom: '8px',
        }}>
          <Space size={4}>
            <span style={{ color: color, fontWeight: 'bold', fontSize: '14px' }}>
              {icon} {title} ({englishName})
            </span>
            <Tooltip title={ProbeHelpInfo[type]} color="#fff" styles={{ root: { color: 'rgba(0,0,0,0.85)' } }}>
              <InfoCircleOutlined style={{ color: '#8c8c8c', cursor: 'pointer' }} />
            </Tooltip>
          </Space>
          {path ? (
            <Tag color={color}>已配置</Tag>
          ) : (
            <Tag color="warning">未配置</Tag>
          )}
        </div>

        <div style={{ marginBottom: '8px' }}>
          <div style={{ color: 'rgba(0,0,0,0.65)', fontSize: '12px' }}>检查端点</div>
          <div style={{ fontWeight: 'bold' }}>{renderProbeEndpoint(path, port)}</div>
        </div>
        
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          <div style={{ 
            padding: '6px 10px', 
            backgroundColor: '#fff', 
            borderRadius: '4px', 
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)',
            minWidth: '90px',
            flex: '1'
          }}>
            <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.65)' }}>初始延迟</div>
            <div style={{ fontWeight: 'bold' }}>{initialDelay || 0} 秒</div>
          </div>
          
          <div style={{ 
            padding: '6px 10px', 
            backgroundColor: '#fff', 
            borderRadius: '4px', 
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)',
            minWidth: '90px',
            flex: '1'
          }}>
            <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.65)' }}>检查间隔</div>
            <div style={{ fontWeight: 'bold' }}>{period || 10} 秒</div>
          </div>
          
          <div style={{ 
            padding: '6px 10px', 
            backgroundColor: '#fff', 
            borderRadius: '4px', 
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)',
            minWidth: '90px',
            flex: '1'
          }}>
            <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.65)' }}>超时时间</div>
            <div style={{ fontWeight: 'bold' }}>{timeout || 1} 秒</div>
          </div>
          
          <div style={{ 
            padding: '6px 10px', 
            backgroundColor: '#fff', 
            borderRadius: '4px', 
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)',
            minWidth: '90px',
            flex: '1'
          }}>
            <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.65)' }}>阈值</div>
            <div style={{ fontWeight: 'bold' }}>{failureThreshold || 3}/{successThreshold || 1}</div>
          </div>
        </div>
      </div>
    );
  };
  
  const renderTimeline = () => {
    const startupDelay = probeConfig.startupInitialDelaySeconds || 60;
    const livenessDelay = probeConfig.livenessInitialDelaySeconds || 30;
    const readinessDelay = probeConfig.readinessInitialDelaySeconds || 20;
    
    return (
      <div style={{ margin: '0 0 10px 0', padding: '10px', backgroundColor: '#fcfcfc', borderRadius: '6px' }}>
        <div style={{ marginBottom: '6px', fontWeight: 'bold', fontSize: '14px' }}>应用生命周期</div>
        <div style={{ position: 'relative', height: '24px', marginBottom: '4px' }}>
          {/* 时间轴线 */}
          <div style={{ 
            position: 'absolute', 
            top: '12px', 
            left: '0', 
            right: '0', 
            height: '2px', 
            backgroundColor: '#e8e8e8', 
            zIndex: 1 
          }} />
          
          {/* 部署点 */}
          <div style={{ 
            position: 'absolute', 
            top: '8px', 
            left: '0', 
            width: '10px', 
            height: '10px', 
            borderRadius: '50%', 
            backgroundColor: '#8c8c8c', 
            zIndex: 2,
            border: '2px solid #fff'
          }} />
          
          {/* 启动完成点 */}
          <div style={{ 
            position: 'absolute', 
            top: '8px', 
            left: '25%', 
            width: '10px', 
            height: '10px', 
            borderRadius: '50%', 
            backgroundColor: probeColorScheme.startup.color, 
            zIndex: 2,
            border: '2px solid #fff'
          }} />
          
          {/* 就绪可用点 */}
          <div style={{ 
            position: 'absolute', 
            top: '8px', 
            left: '50%', 
            width: '10px', 
            height: '10px', 
            borderRadius: '50%', 
            backgroundColor: probeColorScheme.readiness.color, 
            zIndex: 2,
            border: '2px solid #fff'
          }} />
          
          {/* 运行中点 */}
          <div style={{ 
            position: 'absolute', 
            top: '8px', 
            left: '75%', 
            width: '10px', 
            height: '10px', 
            borderRadius: '50%', 
            backgroundColor: probeColorScheme.liveness.color, 
            zIndex: 2,
            border: '2px solid #fff'
          }} />
        </div>
        
        {/* 时间轴标签 */}
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div style={{ textAlign: 'left', fontSize: '11px', width: '25%' }}>部署</div>
          <div style={{ textAlign: 'center', fontSize: '11px', width: '25%', color: probeColorScheme.startup.color }}>启动完成</div>
          <div style={{ textAlign: 'center', fontSize: '11px', width: '25%', color: probeColorScheme.readiness.color }}>就绪可用</div>
          <div style={{ textAlign: 'right', fontSize: '11px', width: '25%', color: probeColorScheme.liveness.color }}>运行中</div>
        </div>
      </div>
    );
  };

  return (
    <Card 
      variant="borderless"
      className="probe-tooltip-card"
      style={{ backgroundColor: '#fff' }}
      styles={{ body: { padding: '0px' } }}
    >
      <div style={{ padding: '8px 12px', display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: '1px solid #f0f0f0' }}>
        <Typography.Title level={5} style={{ margin: 0, fontSize: '15px' }}>
          容器探针配置
        </Typography.Title>
        {editable && onEdit && (
          <Button 
            type="primary" 
            size="small" 
            icon={<EditOutlined />} 
            onClick={onEdit}
          >
            编辑
          </Button>
        )}
      </div>

      <div style={{ padding: '10px' }}>
        {renderTimeline()}
        
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          {renderProbeCard(
            'startup',
            probeConfig.startupProbePath,
            probeConfig.startupProbePort,
            probeConfig.startupInitialDelaySeconds,
            probeConfig.startupTimeoutSeconds,
            probeConfig.startupPeriodSeconds,
            probeConfig.startupFailureThreshold,
            probeConfig.startupSuccessThreshold
          )}
          
          {renderProbeCard(
            'liveness',
            probeConfig.livenessProbePath,
            probeConfig.livenessProbePort,
            probeConfig.livenessInitialDelaySeconds,
            probeConfig.livenessTimeoutSeconds,
            probeConfig.livenessPeriodSeconds,
            probeConfig.livenessFailureThreshold,
            probeConfig.livenessSuccessThreshold
          )}
          
          {renderProbeCard(
            'readiness',
            probeConfig.readinessProbePath,
            probeConfig.readinessProbePort,
            probeConfig.readinessInitialDelaySeconds,
            probeConfig.readinessTimeoutSeconds,
            probeConfig.readinessPeriodSeconds,
            probeConfig.readinessFailureThreshold,
            probeConfig.readinessSuccessThreshold
          )}
        </div>
      </div>
      
      <div style={{ padding: '8px 12px', borderTop: '1px solid #f0f0f0', backgroundColor: '#fafafa' }}>
        <Space>
          <QuestionCircleOutlined style={{ color: '#1890ff' }} />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            探针配置将用于 Kubernetes 部署，确保应用稳定运行并正确处理流量
          </Text>
        </Space>
      </div>
    </Card>
  );
};

export { ProbeTooltip }; 