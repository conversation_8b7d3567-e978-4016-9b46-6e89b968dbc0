.deployment-diagram {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  
  // Common SVG element styling
  svg {
    display: block;
    margin: 0 auto;
    
    text {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
      'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
    }
  }
  
  // Animation classes
  .animated-path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: dash 1.5s linear forwards;
  }
  
  .animated-element {
    opacity: 0;
    animation: fade-in 0.5s ease-in forwards;
    
    &.delay-1 {
      animation-delay: 0.5s;
    }
    
    &.delay-2 {
      animation-delay: 1s;
    }
    
    &.delay-3 {
      animation-delay: 1.5s;
    }
  }
  
  // Stage visibility
  .stage, .stage-0, .stage-1, .stage-2, .stage-3 {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    
    &.active {
      opacity: 1;
    }
  }
  
  // Special element transitions
  .visible-text {
    opacity: 0;
    animation: fade-in 0.5s ease-in forwards;
  }
  
  .hidden-text {
    opacity: 0;
  }
  
  .active {
    opacity: 1;
  }
  
  .hidden {
    opacity: 0;
  }
  
  .faded {
    opacity: 0.5;
    transition: opacity 0.5s ease-in-out;
  }
}

// Blue-Green specific styles
.blue-green-diagram {
  .server-group {
    transition: all 0.5s ease-in-out;
  }
  
  .traffic-path {
    transition: opacity 0.5s ease-in-out;
    
    &.active {
      opacity: 1;
    }
    
    &:not(.active) {
      opacity: 0.2;
    }
  }
  
  .timeline {
    circle {
      transition: all 0.3s ease-in-out;
    }
    
    text {
      transition: all 0.3s ease-in-out;
    }
  }
  
  .switch-indicator {
    text {
      font-weight: bold;
    }
  }
  
  .shutdown-indicator {
    animation: pulse 2s infinite;
  }
}

// Canary specific styles
.canary-diagram {
  .progress-bar {
    transition: width 1s ease-in-out;
  }
  
  .active-step {
    circle {
      animation: pulse 2s infinite;
    }
  }
  
  .stage-content {
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    
    &.active {
      opacity: 1;
    }
  }
  
  .step-2, .step-3 {
    &.active {
      animation: slide-in-right 0.8s ease-in-out forwards;
    }
    
    &.hidden {
      opacity: 0;
    }
  }
  
  .new-version {
    &.active {
      animation: slide-in-right 0.8s ease-in-out forwards;
    }
    
    &.hidden {
      opacity: 0;
    }
  }
}

// Keyframe animations
@keyframes dash {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    filter: drop-shadow(0 0 0px #1890ff80);
  }
  50% {
    filter: drop-shadow(0 0 5px #1890ff80);
  }
  100% {
    filter: drop-shadow(0 0 0px #1890ff80);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 