/* ContainerConfigEditor 组件样式 */

.container-config-editor {
  .ant-modal-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .ant-modal-body {
    background-color: #ffffff;
    padding: 24px;
  }
  
  .ant-modal-footer {
    background-color: #fafafa;
    border-top: 1px solid #e8e8e8;
  }
  
  .modal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .title-icon {
      color: #1890ff;
      font-size: 18px;
    }
    
    .title-app-info {
      font-size: 12px;
    }
  }
  
  // 配置选项卡共享样式
  .tab-content {
    padding: 8px 0;
    
    .section-description {
      margin-bottom: 16px;
      display: inline-block;
    }
    
    .help-icon {
      color: #1890ff;
      margin-left: 8px;
      cursor: pointer;
    }
    
    // 策略图表容器
    .strategy-diagram-card {
      margin: 16px 0;
      
      .strategy-diagram {
        min-height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
      }
    }
    
    // 端口配置
    .port-item {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #fafafa;
      border-radius: 4px;
      position: relative;
      
      .port-header {
        margin-bottom: 12px;
      }
      
      .port-config {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
      }
      
      .remove-port-btn {
        position: absolute;
        top: 12px;
        right: 12px;
      }
    }
    
    // 金丝雀部署步骤
    .canary-step-item {
      position: relative;
      margin-bottom: 16px;
      padding: 16px;
      padding-left: 40px;
      background-color: #fafafa;
      border-radius: 4px;
      
      .step-number {
        position: absolute;
        left: 12px;
        top: 16px;
        width: 20px;
        height: 20px;
        background-color: #1890ff;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
      }
    }
  }
}

/* 专业表单样式 */
.professional-form {
  .ant-card {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-bottom: 16px;
    
    .ant-card-head {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      
      .ant-card-head-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }
    }
    
    .ant-card-body {
      background-color: #ffffff;
      padding: 16px;
    }
  }
  
  .ant-form-item-label > label {
    color: #262626;
    font-weight: 500;
    font-size: 12px;
  }
  
  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    border-color: #d9d9d9;
    font-size: 12px;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .ant-input-number {
    width: 100%;
  }
}

/* 资源配置分组样式 */
.resource-config-group {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  
  .resource-title {
    font-weight: 600;
    font-size: 13px;
    color: #262626;
    margin-bottom: 12px;
    display: block;
  }
  
  .ant-form-item {
    margin-bottom: 8px;
    
    .ant-form-item-label > label {
      color: #595959;
      font-weight: 500;
      font-size: 12px;
      
      &::after {
        color: #8c8c8c;
        font-weight: normal;
      }
    }
  }
  
  .ant-row {
    .ant-col {
      &:first-child {
        .ant-form-item-label > label::after {
          content: " (最小)";
        }
      }
      
      &:last-child {
        .ant-form-item-label > label::after {
          content: " (最大)";
        }
      }
    }
  }
}

/* 端口配置项样式 */
.port-config-item {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  
  .ant-form-item {
    margin-bottom: 8px;
    
    .ant-form-item-label > label {
      color: #262626;
      font-weight: 500;
      font-size: 12px;
    }
  }
  
  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    font-size: 12px;
  }
  
  .delete-button {
    padding-top: 22px;
    
    .ant-btn {
      color: #ff4d4f;
      
      &:hover {
        color: #ff7875;
        background-color: #fff2f0;
      }
    }
  }
}

/* 环境变量配置样式 */
.environment-config {
  .ant-input {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    background-color: #fafafa;
    
    &:focus {
      background-color: #ffffff;
    }
  }
}

/* 表单验证样式 */
.ant-form-item-has-error {
  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    border-color: #ff4d4f;
    
    &:hover {
      border-color: #ff7875;
    }
    
    &:focus {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }
}

/* 按钮样式 */
.ant-btn {
  border-radius: 4px;
  font-weight: 500;
  
  &.ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
    
    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
    
    &:focus {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }
  }
  
  &.ant-btn-dashed {
    border-color: #d9d9d9;
    color: #595959;
    
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
  
  &.ant-btn-text {
    &.ant-btn-dangerous {
      color: #ff4d4f;
      
      &:hover {
        color: #ff7875;
        background-color: #fff2f0;
      }
    }
  }
}

/* 紧凑布局 */
.compact-layout {
  .ant-form-item {
    margin-bottom: 8px;
  }

  .ant-card {
    margin-bottom: 8px;

    .ant-card-head {
      padding: 6px 12px;
      min-height: 28px;

      .ant-card-head-title {
        font-size: 12px;
        line-height: 16px;
      }
    }

    .ant-card-body {
      padding: 8px 12px;
    }
  }

  .ant-form-item-label > label {
    font-size: 11px;
    line-height: 14px;
    margin-bottom: 2px;
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-input-number-input {
    font-size: 11px;
    padding: 2px 6px;
    height: 24px;
    line-height: 20px;
  }

  .ant-select-selection-item {
    font-size: 11px;
    line-height: 20px;
  }

  .ant-btn-sm {
    height: 24px;
    padding: 0 8px;
    font-size: 11px;
  }

  /* Collapse 样式优化 */
  .ant-collapse {
    background: transparent;
    border: none;

    .ant-collapse-item {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #ffffff;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-collapse-header {
      padding: 8px 12px !important;
      background: #fafafa;
      border-radius: 6px 6px 0 0;
      border-bottom: 1px solid #e8e8e8;

      .ant-collapse-arrow {
        font-size: 10px;
      }

      .anticon {
        font-size: 12px;
        margin-right: 6px;
      }

      span {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .ant-collapse-content {
      border: none;
      background: #ffffff;

      .ant-collapse-content-box {
        padding: 12px;
      }
    }
  }
}

/* 超紧凑端口配置 */
.port-row-compact {
  background-color: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 6px;
  padding: 8px 10px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .ant-form-item-label {
    padding-bottom: 2px;

    > label {
      font-size: 10px;
      color: #8c8c8c;
      height: auto;
      line-height: 12px;
      font-weight: 500;
    }
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    height: 24px;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .ant-input-number-input {
    height: 20px;
    padding: 0 6px;
  }

  .ant-btn {
    height: 24px;
    width: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    .anticon {
      font-size: 12px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container-config-editor {
    .ant-modal {
      width: 95% !important;
      max-width: none;
    }
    
    .ant-modal-body {
      padding: 16px;
    }
  }
  
  .professional-form {
    .ant-card-body {
      padding: 12px;
    }
    
    .ant-form-item-label > label {
      font-size: 11px;
    }
    
    .ant-input,
    .ant-input-number,
    .ant-select-selector {
      font-size: 11px;
    }
  }
  
  .resource-config-group {
    padding: 8px;
    
    .resource-title {
      font-size: 12px;
    }
  }
  
  .port-config-item {
    padding: 8px;
    
    .ant-row {
      .ant-col {
        margin-bottom: 8px;
      }
    }
  }
}

/* 策略配置样式 */
.strategy-config {
  .ant-form-item {
    margin-bottom: 12px;
  }

  .strategy-steps {
    .ant-form-item {
      margin-bottom: 8px;
    }
  }

  .canary-step-item {
    background-color: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 8px 10px;
    margin-bottom: 8px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #d9d9d9;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-form-item-label {
      padding-bottom: 2px;

      > label {
        font-size: 10px;
        color: #8c8c8c;
        font-weight: 500;
      }
    }
  }

  .bluegreen-config {
    .ant-form-item-label > label {
      color: #1890ff;
      font-weight: 500;
    }
  }

  .canary-config {
    .ant-form-item-label > label {
      color: #52c41a;
      font-weight: 500;
    }
  }
}

/* 额外的紧凑样式优化 */
.compact-layout {
  /* Modal 标题样式 */
  .ant-modal-header {
    padding: 12px 16px;

    .ant-modal-title {
      font-size: 14px;
      font-weight: 600;

      .anticon {
        font-size: 14px;
        margin-right: 6px;
        color: #1890ff;
      }
    }
  }

  .ant-modal-body {
    padding: 16px;
  }

  .ant-modal-footer {
    padding: 8px 16px;

    .ant-btn {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
    }
  }

  /* 表单标签优化 */
  .ant-form-item-label {
    padding-bottom: 2px;

    > label {
      color: #595959;
      font-weight: 500;
    }
  }

  /* 输入框聚焦效果 */
  .ant-input:focus,
  .ant-input-number:focus,
  .ant-select-focused .ant-select-selector {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  /* 资源配置卡片优化 */
  .ant-card-small > .ant-card-head {
    padding: 6px 8px;
    min-height: 24px;

    .ant-card-head-title {
      font-size: 11px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-card-small > .ant-card-body {
    padding: 8px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .container-config-editor {
    .ant-modal-header {
      background-color: #1f1f1f;
      border-color: #434343;
    }

    .ant-modal-body {
      background-color: #1f1f1f;
    }

    .ant-modal-footer {
      background-color: #262626;
      border-color: #434343;
    }
  }

  .compact-layout {
    .ant-collapse-item {
      background-color: #1f1f1f;
      border-color: #434343;
    }

    .ant-collapse-header {
      background-color: #262626;
      border-color: #434343;

      span {
        color: #f0f0f0;
      }
    }

    .ant-collapse-content {
      background-color: #1f1f1f;
    }

    .ant-card {
      background-color: #1f1f1f;
      border-color: #434343;

      .ant-card-head {
        background-color: #262626;
        border-color: #434343;

        .ant-card-head-title {
          color: #f0f0f0;
        }
      }

      .ant-card-body {
        background-color: #1f1f1f;
      }
    }

    .ant-form-item-label > label {
      color: #f0f0f0;
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector {
      background-color: #262626;
      border-color: #434343;
      color: #f0f0f0;
    }

    .port-row-compact {
      background-color: #262626;
      border-color: #434343;
    }
  }
}
