import React, { useState } from 'react';
import { 
  Form, Input, Select, Row, Col, 
  Typography, Radio, Card, Space, Switch
} from 'antd';
import { 
  ThunderboltOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface RuntimeConfigTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const RuntimeConfigTab: React.FC<RuntimeConfigTabProps> = ({
  form,
  selectedLanguage,
  onFormChange
}) => {
  const [memoryMode, setMemoryMode] = useState('preset');
  const [gcMode, setGcMode] = useState('preset');

  return (
    <div className="tab-content">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Java 运行时配置 */}
        {selectedLanguage === 'java' && (
          <>
            {/* JVM内存配置 */}
            <Card 
              title={
                <Space>
                  <ThunderboltOutlined />
                  <Text strong>JVM内存配置</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Form.Item label="内存配置" name={['runtime_params', 'jvm_memory']}>
                <Radio.Group 
                  buttonStyle="solid"
                  size="large"
                  onChange={(e) => {
                    setMemoryMode(e.target.value.includes('custom:') ? 'custom' : 'preset');
                    onFormChange();
                  }}
                >
                  <Radio.Button value="512m">512MB</Radio.Button>
                  <Radio.Button value="1g">1GB</Radio.Button>
                  <Radio.Button value="2g">2GB</Radio.Button>
                  <Radio.Button value="4g">4GB</Radio.Button>
                  <Radio.Button value="8g">8GB</Radio.Button>
                </Radio.Group>
                <div style={{ marginTop: 12 }}>
                  <Radio
                    value="custom"
                    checked={memoryMode === 'custom'}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setMemoryMode('custom');
                        form.setFieldValue(['runtime_params', 'jvm_memory'], 'custom:');
                      }
                    }}
                  >
                    自定义：
                  </Radio>
                  {memoryMode === 'custom' && (
                    <Input
                      placeholder="-Xmx2g -Xms1g -XX:MetaspaceSize=256m"
                      style={{ width: '70%', marginLeft: 8 }}
                      size="large"
                      onChange={(e) => {
                        form.setFieldValue(['runtime_params', 'jvm_memory'], `custom:${e.target.value}`);
                        onFormChange();
                      }}
                    />
                  )}
                </div>
              </Form.Item>
            </Card>

            {/* GC配置 */}
            <Card 
              title="垃圾回收配置"
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item label="GC策略" name={['runtime_params', 'gc_type']}>
                    <Select 
                      style={{ width: '100%' }}
                      defaultValue="G1GC"
                      size="large"
                      onChange={(value) => {
                        setGcMode(value.toString().includes('custom:') ? 'custom' : 'preset');
                        onFormChange();
                      }}
                    >
                      <Option value="G1GC">G1GC（推荐）</Option>
                      <Option value="ParallelGC">ParallelGC</Option>
                      <Option value="ZGC">ZGC</Option>
                      <Option value="ShenandoahGC">ShenandoahGC</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item
                    label="GC调优参数"
                    name={['runtime_params', 'gc_tuning']}
                  >
                    <Input 
                      placeholder="-XX:MaxGCPauseMillis=200" 
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <div style={{ marginTop: 12 }}>
                <Radio
                  value="custom"
                  checked={gcMode === 'custom'}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setGcMode('custom');
                      form.setFieldValue(['runtime_params', 'gc_type'], 'custom:');
                    }
                  }}
                >
                  自定义GC参数：
                </Radio>
                {gcMode === 'custom' && (
                  <Input
                    placeholder="-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
                    style={{ width: '70%', marginLeft: 8, marginTop: 8 }}
                    size="large"
                    onChange={(e) => {
                      form.setFieldValue(['runtime_params', 'gc_type'], `custom:${e.target.value}`);
                      onFormChange();
                    }}
                  />
                )}
              </div>
            </Card>

            {/* JVM启动参数 */}
            <Card 
              title="JVM启动参数"
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Form.Item
                label="JVM启动参数"
                name={['runtime_params', 'jvm_options']}
              >
                <TextArea 
                  placeholder={`-Dspring.profiles.active=prod
-Dserver.port=8080
-Dfile.encoding=UTF-8
-Djava.security.egd=file:/dev/./urandom
-Duser.timezone=Asia/Shanghai`}
                  autoSize={{ minRows: 4, maxRows: 6 }}
                  onChange={onFormChange}
                />
              </Form.Item>
              
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="启动命令"
                    name={['runtime_params', 'startup_command']}
                  >
                    <Input 
                      placeholder="java -jar /app/application.jar" 
                      size="large"
                      onChange={onFormChange} 
                    />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item
                    label="主类"
                    name={['build_params', 'main_class']}
                  >
                    <Input 
                      placeholder="com.example.Application" 
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </>
        )}

        {/* 非Java语言的运行时配置 */}
        {selectedLanguage !== 'java' && (
          <Card 
            title="运行时配置"
            size="small"
            style={{ marginBottom: 16 }}
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label="内存分配"
                  name={['runtime_params', 'memory_limit']}
                >
                  <Select size="large">
                    <Option value="auto">自动分配</Option>
                    <Option value="512m">512MB</Option>
                    <Option value="1g">1GB</Option>
                    <Option value="2g">2GB</Option>
                    <Option value="4g">4GB</Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={16}>
                <Form.Item
                  label="启动命令"
                  name={['runtime_params', 'startup_command']}
                >
                  <Input 
                    placeholder="启动命令" 
                    size="large"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        {/* 环境变量配置 */}
        <Card 
          title="环境变量配置"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Form.Item
            label="环境变量"
            name={['runtime_params', 'environment_variables']}
            extra="每行一个环境变量，格式为 KEY=VALUE"
          >
            <TextArea 
              placeholder={`DATABASE_URL=************************
REDIS_URL=redis://redis:6379
LOG_LEVEL=INFO
SPRING_PROFILES_ACTIVE=prod`}
              autoSize={{ minRows: 4, maxRows: 6 }}
            />
          </Form.Item>
        </Card>

        {/* 健康检查配置 */}
        <Card 
          title="健康检查配置"
          size="small"
        >
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                label="启用健康检查"
                name={['runtime_params', 'health_check_enabled']}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={6}>
              <Form.Item
                label="检查路径"
                name={['runtime_params', 'health_check_path']}
              >
                <Input 
                  placeholder="/actuator/health" 
                  size="large"
                />
              </Form.Item>
            </Col>
            
            <Col span={4}>
              <Form.Item
                label="初始延迟"
                name={['runtime_params', 'health_check_initial_delay']}
              >
                <Input 
                  placeholder="30s" 
                  size="large"
                />
              </Form.Item>
            </Col>
            
            <Col span={4}>
              <Form.Item
                label="检查间隔"
                name={['runtime_params', 'health_check_interval']}
              >
                <Input 
                  placeholder="10s" 
                  size="large"
                />
              </Form.Item>
            </Col>
            
            <Col span={4}>
              <Form.Item
                label="超时时间"
                name={['runtime_params', 'health_check_timeout']}
              >
                <Input 
                  placeholder="5s" 
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Space>
    </div>
  );
};
