import React from 'react';
import { 
  Form, Input, Select, Row, Col, 
  Typography, Switch, Radio, Card, Space
} from 'antd';
import { 
  ToolOutlined
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface BuildParamsTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const BuildParamsTab: React.FC<BuildParamsTabProps> = ({
  form,
  selectedLanguage,
  onFormChange
}) => {
  return (
    <div className="tab-content">
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Java 构建参数 */}
        {selectedLanguage === 'java' && (
          <>
            {/* 基础构建配置 */}
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label="Java版本"
                  name={['build_params', 'java_version']}
                  rules={[{ required: true, message: '请选择Java版本' }]}
                >
                  <Select placeholder="选择版本" size="large">
                    <Option value="8">Java 8</Option>
                    <Option value="11">Java 11</Option>
                    <Option value="17">Java 17</Option>
                    <Option value="21">Java 21</Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item
                  label="构建工具"
                  name={['build_params', 'build_tool']}
                >
                  <Select placeholder="选择构建工具" size="large">
                    <Option value="auto">自动检测</Option>
                    <Option value="maven">Maven</Option>
                    <Option value="gradle">Gradle</Option>
                  </Select>
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <Form.Item
                  label="编译器选项"
                  name={['build_params', 'compiler_options']}
                >
                  <Input 
                    placeholder="-source 17 -target 17 -encoding UTF-8" 
                    size="large"
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 构建选项 */}
            <Card 
              title={
                <Space>
                  <ToolOutlined />
                  <Text strong>构建选项</Text>
                </Space>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="跳过测试"
                    name={['build_params', 'skip_tests']}
                    valuePropName="checked"
                  >
                    <Switch onChange={onFormChange} />
                  </Form.Item>
                </Col>
                
                <Col span={6}>
                  <Form.Item
                    label="并行构建"
                    name={['build_params', 'parallel_build']}
                    valuePropName="checked"
                  >
                    <Switch onChange={onFormChange} />
                  </Form.Item>
                </Col>
                
                <Col span={6}>
                  <Form.Item
                    label="离线模式"
                    name={['build_params', 'offline_mode']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                
                <Col span={6}>
                  <Form.Item
                    label="强制更新依赖"
                    name={['build_params', 'force_update']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    label="并行线程数"
                    name={['build_params', 'parallel_threads']}
                  >
                    <Input placeholder="2C" size="large" />
                  </Form.Item>
                </Col>
                
                <Col span={8}>
                  <Form.Item
                    label="Maven配置文件"
                    name={['build_params', 'maven_settings']}
                  >
                    <Input placeholder="settings.xml" size="large" />
                  </Form.Item>
                </Col>
                
                <Col span={8}>
                  <Form.Item
                    label="Maven Profiles"
                    name={['build_params', 'maven_profiles']}
                  >
                    <Input placeholder="prod,docker,optimization" size="large" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label="测试参数"
                    name={['build_params', 'test_params']}
                  >
                    <Input 
                      placeholder="-Dtest=UserServiceTest,IntegrationTest" 
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 依赖管理 */}
            <Card 
              title="依赖管理"
              size="small"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label="私有仓库"
                    name={['build_params', 'private_repo']}
                  >
                    <Input 
                      placeholder="https://nexus.company.com/repository/maven-public" 
                      size="large"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="用户名"
                    name={['build_params', 'repo_username']}
                  >
                    <Input placeholder="deploy_user" size="large" />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item
                    label="密码"
                    name={['build_params', 'repo_password']}
                  >
                    <Input.Password placeholder="••••••••" size="large" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 构建脚本 */}
            <Card 
              title="构建脚本"
              size="small"
            >
              <Form.Item
                label="构建前脚本"
                name="pre_build_script"
                extra="在构建开始前执行的脚本"
              >
                <TextArea 
                  placeholder={`#!/bin/bash
echo "Starting build at $(date)"
export JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF-8"
export MAVEN_OPTS="-Xmx2g -XX:+UseG1GC"`}
                  autoSize={{ minRows: 4, maxRows: 6 }}
                />
              </Form.Item>
              
              <Form.Item
                label="自定义构建脚本"
                name="custom_build_script"
                extra="完全自定义的构建脚本，会覆盖默认构建流程"
              >
                <TextArea 
                  placeholder={`#!/bin/bash
set -e
echo "🚀 开始构建..."
mvn clean package -DskipTests -T 2C -Pprod,docker
if [ $? -eq 0 ]; then
  echo "✅ 构建成功"
  ls -la target/*.jar
else
  echo "❌ 构建失败"
  exit 1
fi`}
                  autoSize={{ minRows: 6, maxRows: 10 }}
                />
              </Form.Item>
              
              <Form.Item
                label="构建后脚本"
                name="post_build_script"
                extra="在构建完成后执行的脚本"
              >
                <TextArea 
                  placeholder={`#!/bin/bash
echo "📦 构建产物信息:"
du -h target/*.jar
echo "🏷️ 构建标签: \${BUILD_TAG:-latest}"`}
                  autoSize={{ minRows: 3, maxRows: 5 }}
                />
              </Form.Item>
            </Card>
          </>
        )}

        {/* Go 构建参数 */}
        {selectedLanguage === 'go' && (
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="Go版本"
                name={['build_params', 'go_version']}
                rules={[{ required: true, message: '请选择Go版本' }]}
              >
                <Select size="large">
                  <Option value="1.19">Go 1.19</Option>
                  <Option value="1.20">Go 1.20</Option>
                  <Option value="1.21">Go 1.21</Option>
                  <Option value="1.22">Go 1.22</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="构建工具"
                name={['build_params', 'build_tool']}
              >
                <Radio.Group buttonStyle="solid" size="large">
                  <Radio.Button value="go">Go Modules</Radio.Button>
                  <Radio.Button value="make">Makefile</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="CGO启用"
                name={['build_params', 'cgo_enabled']}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        )}

        {/* Node.js 构建参数 */}
        {selectedLanguage === 'nodejs' && (
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="Node.js版本"
                name={['build_params', 'node_version']}
                rules={[{ required: true, message: '请选择Node.js版本' }]}
              >
                <Select size="large">
                  <Option value="16">Node.js 16</Option>
                  <Option value="18">Node.js 18</Option>
                  <Option value="20">Node.js 20</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="包管理器"
                name={['build_params', 'package_manager']}
              >
                <Radio.Group buttonStyle="solid" size="large">
                  <Radio.Button value="npm">npm</Radio.Button>
                  <Radio.Button value="yarn">yarn</Radio.Button>
                  <Radio.Button value="pnpm">pnpm</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="构建命令"
                name={['build_params', 'build_command']}
              >
                <Input placeholder="npm run build" size="large" />
              </Form.Item>
            </Col>
          </Row>
        )}

        {/* Python 构建参数 */}
        {selectedLanguage === 'python' && (
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="Python版本"
                name={['build_params', 'python_version']}
                rules={[{ required: true, message: '请选择Python版本' }]}
              >
                <Select size="large">
                  <Option value="3.8">Python 3.8</Option>
                  <Option value="3.9">Python 3.9</Option>
                  <Option value="3.10">Python 3.10</Option>
                  <Option value="3.11">Python 3.11</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="包管理器"
                name={['build_params', 'package_manager']}
              >
                <Radio.Group buttonStyle="solid" size="large">
                  <Radio.Button value="pip">pip</Radio.Button>
                  <Radio.Button value="poetry">Poetry</Radio.Button>
                  <Radio.Button value="pipenv">Pipenv</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="依赖文件"
                name={['build_params', 'requirements_file']}
              >
                <Input placeholder="requirements.txt" size="large" />
              </Form.Item>
            </Col>
          </Row>
        )}

        {/* PHP 构建参数 */}
        {selectedLanguage === 'php' && (
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="PHP版本"
                name={['build_params', 'php_version']}
                rules={[{ required: true, message: '请选择PHP版本' }]}
              >
                <Select size="large">
                  <Option value="7.4">PHP 7.4</Option>
                  <Option value="8.0">PHP 8.0</Option>
                  <Option value="8.1">PHP 8.1</Option>
                  <Option value="8.2">PHP 8.2</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="包管理器"
                name={['build_params', 'package_manager']}
              >
                <Radio.Group buttonStyle="solid" size="large">
                  <Radio.Button value="composer">Composer</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="启用扩展"
                name={['build_params', 'php_extensions']}
              >
                <Input placeholder="pdo,mysqli,gd" size="large" />
              </Form.Item>
            </Col>
          </Row>
        )}
      </Space>
    </div>
  );
};
