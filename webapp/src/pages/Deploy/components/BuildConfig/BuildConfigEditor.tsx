import React, { useState, useEffect } from 'react';
import {
  Modal, Form, Button, Space,
  message, Tabs, Typography
} from 'antd';
import {
  RocketOutlined
} from '@ant-design/icons';
import { BasicConfigTab } from './ConfigTabs/BasicConfigTab';
import { BuildParamsTab } from './ConfigTabs/BuildParamsTab';
import { RuntimeConfigTab } from './ConfigTabs/RuntimeConfigTab';
import { ConfigPreviewPanel } from './ConfigPreviewPanel';
import './BuildConfigEditor.less';

const { Text } = Typography;

export interface BuildConfigEditorProps {
  visible: boolean;
  appData: API.AppData | null;
  onCancel: () => void;
  onSave: (updatedSettings: any) => Promise<boolean>;
}

const BuildConfigEditor: React.FC<BuildConfigEditorProps> = ({
  visible,
  appData,
  onCancel,
  onSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('java');
  const [selectedTemplate, setSelectedTemplate] = useState('smart-detect');
  const [activeTab, setActiveTab] = useState('basic');
  const [previewCommand, setPreviewCommand] = useState('');
  const [formValues, setFormValues] = useState<any>(null);

  useEffect(() => {
    if (visible && appData?.application) {
      const currentConfig = {
        language: appData.application.language || 'java',
        app_type: 'web',
        template_type: 'smart-detect',
        app_name: appData.application.name || 'my-application',
        work_dir: '/app',
        build_params: {
          java_version: '17',
          build_tool: 'auto',
          compiler_options: '-source 17 -target 17 -encoding UTF-8',
          skip_tests: true,
          parallel_build: true,
          parallel_threads: '2C',
          maven_settings: 'settings.xml',
          maven_profiles: 'prod,docker',
          test_params: '',
          offline_mode: false,
          force_update: false,
          private_repo: '',
          repo_username: '',
          repo_password: '',
          main_class: '',
          go_version: '1.21',
          node_version: '18',
          python_version: '3.11',
          php_version: '8.2',
          package_manager: 'auto',
          cgo_enabled: false,
          build_command: '',
          requirements_file: 'requirements.txt',
          php_extensions: ''
        },
        runtime_params: {
          jvm_memory: '1g',
          gc_type: 'G1GC',
          gc_tuning: '',
          jvm_options: '-Dspring.profiles.active=prod\n-Dserver.port=8080\n-Dfile.encoding=UTF-8',
          startup_command: 'java -jar /app/application.jar',
          environment_variables: 'DATABASE_URL=**********************************************************************',
          health_check_enabled: true,
          health_check_path: '/actuator/health',
          health_check_initial_delay: '30s',
          health_check_interval: '10s',
          health_check_timeout: '5s',
          memory_limit: '1g'
        },
        container_params: {
          replicas: 1,
          namespace: 'default',
          port: 8080,
          expose_ports: '',
          volumes: '',
          base_image: 'openjdk:17-jre-slim',
          cpu_request: 200,
          cpu_limit: 1000,
          memory_request: 512,
          memory_limit: 1024
        },
        pre_build_script: '',
        post_build_script: '',
        custom_build_script: ''
      };

      form.setFieldsValue(currentConfig);
      setSelectedLanguage(currentConfig.language);
      setSelectedTemplate(currentConfig.template_type);
      setFormValues(currentConfig);
    }
  }, [visible, appData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const buildConfig = {
        language: values.language,
        app_type: values.app_type,
        template_type: values.template_type,
        app_name: values.app_name,
        work_dir: values.work_dir,
        build_params: values.build_params,
        runtime_params: values.runtime_params,
        container_params: values.container_params,
        pre_build_script: values.pre_build_script,
        post_build_script: values.post_build_script,
        custom_build_script: values.custom_build_script
      };

      const success = await onSave(buildConfig);
      if (success) {
        message.success('构建配置已成功保存');
        onCancel();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);

    // 重置模板和基础参数
    const defaults = {
      java: {
        template_type: 'smart-detect',
        port: 8080,
        build_params: {
          java_version: '17',
          build_tool: 'auto',
          compiler_options: '-source 17 -target 17 -encoding UTF-8'
        },
        base_image: 'openjdk:17-jre-slim'
      },
      go: {
        template_type: 'gin-api',
        port: 8080,
        build_params: {
          go_version: '1.21',
          build_tool: 'go',
          cgo_enabled: false
        },
        base_image: 'golang:1.21-alpine'
      },
      nodejs: {
        template_type: 'express-api',
        port: 3000,
        build_params: {
          node_version: '18',
          package_manager: 'npm',
          build_command: 'npm run build'
        },
        base_image: 'node:18-alpine'
      },
      python: {
        template_type: 'flask-api',
        port: 5000,
        build_params: {
          python_version: '3.11',
          package_manager: 'pip',
          requirements_file: 'requirements.txt'
        },
        base_image: 'python:3.11-slim'
      },
      php: {
        template_type: 'laravel',
        port: 80,
        build_params: {
          php_version: '8.2',
          package_manager: 'composer',
          php_extensions: 'pdo,mysqli,gd'
        },
        base_image: 'php:8.2-fpm-alpine'
      }
    };

    const langDefaults = defaults[language as keyof typeof defaults];
    setSelectedTemplate(langDefaults.template_type);

    form.setFieldsValue({
      template_type: langDefaults.template_type,
      build_params: {
        ...form.getFieldValue('build_params'),
        ...langDefaults.build_params
      },
      container_params: {
        ...form.getFieldValue('container_params'),
        port: langDefaults.port,
        base_image: langDefaults.base_image
      }
    });

    handleFormChange();
  };

  const handleTemplateChange = (templateType: string) => {
    setSelectedTemplate(templateType);
    handleFormChange();
  };

  const generatePreviewCommand = () => {
    const values = form.getFieldsValue();
    const runtimeParams = values.runtime_params || {};

    let command = '';

    if (runtimeParams.jvm_memory && runtimeParams.jvm_memory !== 'auto') {
      if (runtimeParams.jvm_memory.includes('custom:')) {
        command += runtimeParams.jvm_memory.replace('custom:', '') + ' ';
      } else {
        const memory = runtimeParams.jvm_memory;
        const memoryValue = memory.replace(/[^0-9]/g, '');
        const memoryUnit = memory.replace(/[0-9]/g, '');
        command += `-Xmx${memory} -Xms${Math.floor(parseInt(memoryValue) * 0.5)}${memoryUnit} `;
      }
    }

    if (runtimeParams.gc_type) {
      if (runtimeParams.gc_type.includes('custom:')) {
        command += runtimeParams.gc_type.replace('custom:', '') + ' ';
      } else {
        switch (runtimeParams.gc_type) {
          case 'G1GC':
            command += '-XX:+UseG1GC ';
            break;
          case 'ParallelGC':
            command += '-XX:+UseParallelGC ';
            break;
          case 'ZGC':
            command += '-XX:+UseZGC ';
            break;
          case 'ShenandoahGC':
            command += '-XX:+UseShenandoahGC ';
            break;
        }
      }
    }

    if (runtimeParams.gc_tuning) {
      command += runtimeParams.gc_tuning + ' ';
    }

    if (runtimeParams.jvm_options) {
      const jvmOptions = runtimeParams.jvm_options.split('\n').filter(line => line.trim());
      command += jvmOptions.join(' ') + ' ';
    }

    if (runtimeParams.startup_command) {
      command += runtimeParams.startup_command;
    } else {
      command += 'java -jar /app/application.jar';
    }

    return command.trim();
  };

  const handleFormChange = () => {
    const values = form.getFieldsValue();
    setFormValues(values);

    if (selectedLanguage === 'java') {
      setPreviewCommand(generatePreviewCommand());
    }
  };

  // 定义Tab项目
  const tabItems = [
    {
      key: 'basic',
      label: '基础配置',
      children: (
        <BasicConfigTab
          form={form}
          selectedLanguage={selectedLanguage}
          onLanguageChange={handleLanguageChange}
          onTemplateChange={handleTemplateChange}
        />
      )
    },
    {
      key: 'build',
      label: '构建参数',
      children: (
        <BuildParamsTab
          form={form}
          selectedLanguage={selectedLanguage}
          onFormChange={handleFormChange}
        />
      )
    },
    {
      key: 'runtime',
      label: '运行时',
      children: (
        <RuntimeConfigTab
          form={form}
          selectedLanguage={selectedLanguage}
          onFormChange={handleFormChange}
        />
      )
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <RocketOutlined />
          <span>构建配置 (Build Configuration)</span>
        </Space>
      }
      open={visible}
      width={1200}
      onCancel={onCancel}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          保存配置
        </Button>
      ]}
      destroyOnClose
      className="build-config-editor"
    >
      <div className="config-layout">
        <div className="config-content">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              language: 'java',
              template_type: 'smart-detect'
            }}
            onValuesChange={handleFormChange}
          >
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
              className="config-tabs"
              size="large"
              type="card"
            />
          </Form>
        </div>

        <div className="preview-panel">
          <ConfigPreviewPanel
            formValues={formValues}
            selectedLanguage={selectedLanguage}
            previewCommand={previewCommand}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BuildConfigEditor;