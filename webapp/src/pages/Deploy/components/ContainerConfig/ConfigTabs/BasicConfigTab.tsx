import React from 'react';
import { 
  Form, Input, InputNumber, Select, Row, Col, 
  Card, Divider, Tooltip, Typography, Slider, Space, Progress
} from 'antd';
import { 
  ApiOutlined, DatabaseOutlined, AppstoreOutlined, 
  SettingOutlined, CloudServerOutlined, TeamOutlined, InfoCircleOutlined 
} from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;

interface BasicConfigTabProps {
  form: any;
}

// CPU值转换为滑块数值（500m => 0.5)
const cpuToSliderValue = (cpu: string): number => {
  if (!cpu) return 0;
  if (cpu.endsWith('m')) {
    return parseInt(cpu.replace('m', '')) / 1000;
  }
  return parseFloat(cpu);
};

// 滑块数值转换为CPU值
const sliderValueToCpu = (value: number): string => {
  if (value < 1) {
    return `${Math.round(value * 1000)}m`;
  }
  return `${value}`;
};

// 内存值转换为滑块数值
const memoryToSliderValue = (memory: string): number => {
  if (!memory) return 0;
  
  const value = parseInt(memory.replace(/[^0-9]/g, ''));
  if (memory.includes('Gi')) {
    return value * 1024;
  }
  return value;
};

// 滑块数值转换为内存值
const sliderValueToMemory = (value: number, unit: 'Mi' | 'Gi'): string => {
  if (unit === 'Gi') {
    return `${value / 1024}Gi`;
  }
  return `${value}Mi`;
};

const BasicConfigTab: React.FC<BasicConfigTabProps> = React.memo(({ form }) => {
  const [cpuRequestValue, setCpuRequestValue] = React.useState<number>(0.5);
  const [cpuLimitValue, setCpuLimitValue] = React.useState<number>(1);
  const [memoryRequestValue, setMemoryRequestValue] = React.useState<number>(512);
  const [memoryLimitValue, setMemoryLimitValue] = React.useState<number>(1024);
  
  React.useEffect(() => {
    // 初始化滑块值
    const resources = form.getFieldValue('resources');
    if (resources) {
      if (resources.requests?.cpu) {
        setCpuRequestValue(cpuToSliderValue(resources.requests.cpu));
      }
      if (resources.limits?.cpu) {
        setCpuLimitValue(cpuToSliderValue(resources.limits.cpu));
      }
      if (resources.requests?.memory) {
        setMemoryRequestValue(memoryToSliderValue(resources.requests.memory));
      }
      if (resources.limits?.memory) {
        setMemoryLimitValue(memoryToSliderValue(resources.limits.memory));
      }
    }
  }, [form]);

  // 处理CPU请求变化
  const handleCpuRequestChange = (value: number) => {
    // 确保CPU请求不超过限制
    const validValue = Math.min(value, cpuLimitValue);
    setCpuRequestValue(validValue);
    form.setFieldsValue({
      resources: {
        ...form.getFieldValue('resources'),
        requests: {
          ...form.getFieldValue('resources')?.requests,
          cpu: sliderValueToCpu(validValue)
        }
      }
    });
  };

  // 处理CPU限制变化
  const handleCpuLimitChange = (value: number) => {
    // 确保CPU限制不低于请求
    const validValue = Math.max(value, cpuRequestValue);
    setCpuLimitValue(validValue);
    form.setFieldsValue({
      resources: {
        ...form.getFieldValue('resources'),
        limits: {
          ...form.getFieldValue('resources')?.limits,
          cpu: sliderValueToCpu(validValue)
        }
      }
    });
  };

  // 处理内存请求变化
  const handleMemoryRequestChange = (value: number) => {
    // 确保内存请求不超过限制
    const validValue = Math.min(value, memoryLimitValue);
    setMemoryRequestValue(validValue);
    form.setFieldsValue({
      resources: {
        ...form.getFieldValue('resources'),
        requests: {
          ...form.getFieldValue('resources')?.requests,
          memory: sliderValueToMemory(validValue, 'Mi')
        }
      }
    });
  };

  // 处理内存限制变化
  const handleMemoryLimitChange = (value: number) => {
    // 确保内存限制不低于请求
    const validValue = Math.max(value, memoryRequestValue);
    setMemoryLimitValue(validValue);
    form.setFieldsValue({
      resources: {
        ...form.getFieldValue('resources'),
        limits: {
          ...form.getFieldValue('resources')?.limits,
          memory: sliderValueToMemory(validValue, 'Mi')
        }
      }
    });
  };

  // CPU格式化提示
  const cpuFormatter = (value?: number) => {
    if (value === undefined) return '';
    return value < 1 ? `${value * 1000}m` : `${value}`;
  };

  // 内存格式化提示
  const memoryFormatter = (value?: number) => {
    if (value === undefined) return '';
    return value >= 1024 ? `${(value / 1024).toFixed(1)}Gi` : `${value}Mi`;
  };

  // 计算百分比
  const calculatePercent = (request: number, limit: number): number => {
    return Math.min(Math.round((request / limit) * 100), 100);
  };

  return (
    <div className="tab-content basic-config-tab">
      {/* 基础设置区域 */}
      <Card 
        className="config-card"
        bordered={false}
        bodyStyle={{ padding: '20px', backgroundColor: '#f9fafc', borderRadius: '8px' }}
      >
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          backgroundColor: '#f0f5ff',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #2F54EB'
        }}>
          <AppstoreOutlined style={{ fontSize: '18px', color: '#2F54EB', marginRight: '10px' }} />
          <Title level={5} style={{ margin: 0, color: '#1D39C4' }}>应用基础配置</Title>
        </div>

        <div style={{ backgroundColor: '#fff', borderRadius: '6px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }}>
          <Row gutter={[24, 16]}>
            <Col span={8}>
              <Form.Item
                name="namespace"
                label={<span style={{ fontSize: '14px', fontWeight: 500 }}>命名空间</span>}
                tooltip={{ title: "Kubernetes命名空间，用于资源隔离", color: 'blue' }}
                rules={[{ required: true, message: '请选择命名空间' }]}
              >
                <Select 
                  placeholder="选择命名空间" 
                  size="middle"
                  suffixIcon={<TeamOutlined style={{ color: '#2F54EB' }} />}
                >
                  <Option value="default">default</Option>
                  <Option value="production">production</Option>
                  <Option value="staging">staging</Option>
                  <Option value="testing">testing</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={10}>
              <Form.Item
                name="base_image"
                label={<span style={{ fontSize: '14px', fontWeight: 500 }}>基础镜像</span>}
                tooltip={{ title: "选择容器基础镜像，包含运行环境", color: 'blue' }}
                rules={[{ required: true, message: '请选择基础镜像' }]}
              >
                <Select 
                  placeholder="选择基础镜像" 
                  size="middle"
                  suffixIcon={<CloudServerOutlined style={{ color: '#2F54EB' }} />}
                  showSearch
                  optionFilterProp="children"
                >
                  <Option value="openjdk:17-jre-slim">openjdk:17-jre-slim</Option>
                  <Option value="openjdk:11-jre-slim">openjdk:11-jre-slim</Option>
                  <Option value="openjdk:8-jre-slim">openjdk:8-jre-slim</Option>
                  <Option value="node:18-alpine">node:18-alpine</Option>
                  <Option value="node:16-alpine">node:16-alpine</Option>
                  <Option value="python:3.10-slim">python:3.10-slim</Option>
                  <Option value="python:3.9-slim">python:3.9-slim</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="replicas"
                label={<span style={{ fontSize: '14px', fontWeight: 500 }}>副本数</span>}
                tooltip={{ title: "设置应用运行的Pod数量", color: 'blue' }}
                rules={[
                  { required: true, message: '请输入副本数' },
                  { type: 'integer', min: 1, max: 10, message: '副本数必须在1-10之间' }
                ]}
              >
                <InputNumber
                  min={1}
                  max={10}
                  style={{ width: '100%' }}
                  placeholder="1"
                  size="middle"
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Card>

      {/* 资源配置区域 */}
      <Card 
        className="config-card"
        bordered={false}
        bodyStyle={{ padding: '20px', backgroundColor: '#f9fafc', borderRadius: '8px' }}
        style={{ marginTop: '20px' }}
      >
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          backgroundColor: '#f6ffed',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #52c41a'
        }}>
          <SettingOutlined style={{ fontSize: '18px', color: '#52c41a', marginRight: '10px' }} />
          <Title level={5} style={{ margin: 0, color: '#389e0d' }}>容器资源配置</Title>
        </div>
        
        <Row gutter={[24, 24]}>
          <Col span={12}>
            <div className="resource-gauge" style={{ 
              backgroundColor: '#fff', 
              borderRadius: '6px', 
              padding: '20px', 
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
              height: '100%'
            }}>
              <div style={{ 
                textAlign: 'center', 
                marginBottom: '16px', 
                borderBottom: '1px solid #f0f0f0',
                paddingBottom: '10px'
              }}>
                <Space>
                  <ApiOutlined style={{ fontSize: '18px', color: '#1890ff' }} />
                  <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>CPU资源配置</Text>
                </Space>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'center', position: 'relative' }}>
                <Progress
                  type="dashboard"
                  percent={calculatePercent(cpuRequestValue, cpuLimitValue)}
                  format={() => (
                    <div style={{ fontSize: '14px', lineHeight: '1.2' }}>
                      <div>请求</div>
                      <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                        {sliderValueToCpu(cpuRequestValue)}
                      </div>
                    </div>
                  )}
                  width={140}
                  strokeColor="#1890ff"
                  trailColor="#f0f0f0"
                  strokeWidth={10}
                />
                <div style={{ 
                  position: 'absolute', 
                  bottom: '-10px', 
                  right: '22%',
                  background: '#fff',
                  padding: '4px 10px',
                  borderRadius: '12px',
                  border: '1px solid #d9d9d9',
                  fontSize: '12px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  限制: <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{sliderValueToCpu(cpuLimitValue)}</span>
                </div>
              </div>
              
              <Row gutter={[16, 16]} style={{ marginTop: '30px' }}>
                <Col span={12}>
                  <div style={{ marginBottom: '8px' }}>
                    <Text style={{ fontSize: '14px', color: '#1890ff' }}>请求值:</Text>
                    <Tooltip title="设置容器保证可用的最小CPU资源">
                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#1890ff' }} />
                    </Tooltip>
                  </div>
                  <InputNumber
                    min={0.1}
                    max={Math.max(4, cpuLimitValue)}
                    step={0.1}
                    value={cpuRequestValue}
                    onChange={(value) => value !== null && handleCpuRequestChange(value)}
                    addonAfter="核"
                    style={{ width: '100%' }}
                    size="middle"
                  />
                  <Form.Item
                    name={['resources', 'requests', 'cpu']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <div style={{ marginBottom: '8px' }}>
                    <Text style={{ fontSize: '14px', color: '#1890ff' }}>限制值:</Text>
                    <Tooltip title="设置容器可使用的最大CPU资源">
                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#1890ff' }} />
                    </Tooltip>
                  </div>
                  <InputNumber
                    min={Math.max(0.1, cpuRequestValue)}
                    max={8}
                    step={0.1}
                    value={cpuLimitValue}
                    onChange={(value) => value !== null && handleCpuLimitChange(value)}
                    addonAfter="核"
                    style={{ width: '100%' }}
                    size="middle"
                  />
                  <Form.Item
                    name={['resources', 'limits', 'cpu']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Col>
          
          <Col span={12}>
            <div className="resource-gauge" style={{ 
              backgroundColor: '#fff', 
              borderRadius: '6px', 
              padding: '20px', 
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
              height: '100%'
            }}>
              <div style={{ 
                textAlign: 'center', 
                marginBottom: '16px', 
                borderBottom: '1px solid #f0f0f0',
                paddingBottom: '10px'
              }}>
                <Space>
                  <DatabaseOutlined style={{ fontSize: '18px', color: '#52c41a' }} />
                  <Text strong style={{ fontSize: '16px', color: '#52c41a' }}>内存资源配置</Text>
                </Space>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'center', position: 'relative' }}>
                <Progress
                  type="dashboard"
                  percent={calculatePercent(memoryRequestValue, memoryLimitValue)}
                  format={() => (
                    <div style={{ fontSize: '14px', lineHeight: '1.2' }}>
                      <div>请求</div>
                      <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
                        {memoryFormatter(memoryRequestValue)}
                      </div>
                    </div>
                  )}
                  width={140}
                  strokeColor="#52c41a"
                  trailColor="#f0f0f0"
                  strokeWidth={10}
                />
                <div style={{ 
                  position: 'absolute', 
                  bottom: '-10px', 
                  right: '22%',
                  background: '#fff',
                  padding: '4px 10px',
                  borderRadius: '12px',
                  border: '1px solid #d9d9d9',
                  fontSize: '12px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  限制: <span style={{ fontWeight: 'bold', color: '#52c41a' }}>{memoryFormatter(memoryLimitValue)}</span>
                </div>
              </div>
              
              <Row gutter={[16, 16]} style={{ marginTop: '30px' }}>
                <Col span={12}>
                  <div style={{ marginBottom: '8px' }}>
                    <Text style={{ fontSize: '14px', color: '#52c41a' }}>请求值:</Text>
                    <Tooltip title="设置容器保证可用的最小内存资源">
                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#52c41a' }} />
                    </Tooltip>
                  </div>
                  <InputNumber
                    min={128}
                    max={memoryLimitValue}
                    step={128}
                    value={memoryRequestValue}
                    onChange={(value) => value !== null && handleMemoryRequestChange(value)}
                    addonAfter="Mi"
                    style={{ width: '100%' }}
                    size="middle"
                  />
                  <Form.Item
                    name={['resources', 'requests', 'memory']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <div style={{ marginBottom: '8px' }}>
                    <Text style={{ fontSize: '14px', color: '#52c41a' }}>限制值:</Text>
                    <Tooltip title="设置容器可使用的最大内存资源">
                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#52c41a' }} />
                    </Tooltip>
                  </div>
                  <InputNumber
                    min={memoryRequestValue}
                    max={8192}
                    step={256}
                    value={memoryLimitValue}
                    onChange={(value) => value !== null && handleMemoryLimitChange(value)}
                    addonAfter="Mi"
                    style={{ width: '100%' }}
                    size="middle"
                  />
                  <Form.Item
                    name={['resources', 'limits', 'memory']}
                    hidden
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
        
        <div className="resource-tips" style={{ 
          marginTop: '16px', 
          fontSize: '13px', 
          padding: '12px', 
          backgroundColor: '#e6f7ff', 
          borderRadius: '6px', 
          border: '1px solid #91d5ff' 
        }}>
          <Space>
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
            <Text type="secondary" style={{ fontSize: '13px' }}>
              提示: 请求值表示保证分配的资源，限制值表示最大可用资源。CPU的1000m相当于1核心。合理配置资源可以提高应用性能和稳定性。
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
});

export default BasicConfigTab; 