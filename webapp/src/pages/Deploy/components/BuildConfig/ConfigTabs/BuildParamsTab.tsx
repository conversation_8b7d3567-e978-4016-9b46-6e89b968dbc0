import React from 'react';
import {
  Form, Input, Select, Radio, Switch, Row, Col, Card, Space
} from 'antd';

const { Option } = Select;
const { TextArea } = Input;

interface BuildParamsTabProps {
  form: any;
  selectedLanguage: string;
  onFormChange: () => void;
}

export const BuildParamsTab: React.FC<BuildParamsTabProps> = ({
  form,
  selectedLanguage,
  onFormChange
}) => {
  return (
    <div className="tab-content">
      <div style={{ padding: '20px' }}>
        <h3>构建参数配置</h3>
        <p>当前选择的语言: {selectedLanguage}</p>

        {selectedLanguage === 'java' && (
          <div>
            <Card title="Java 构建参数" style={{ marginBottom: 16 }}>
                <Col span={8}>
                  <Form.Item
                    label="Java版本"
                    name={['build_params', 'java_version']}
                    rules={[{ required: true, message: '请选择Java版本' }]}
                  >
                    <Select size="large" placeholder="选择Java版本">
                      <Option value="8">Java 8</Option>
                      <Option value="11">Java 11</Option>
                      <Option value="17">Java 17</Option>
                      <Option value="21">Java 21</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="构建工具"
                    name={['build_params', 'build_tool']}
                  >
                    <Radio.Group buttonStyle="solid" size="large">
                      <Radio.Button value="maven">Maven</Radio.Button>
                      <Radio.Button value="gradle">Gradle</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="跳过测试"
                    name={['build_params', 'skip_tests']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </div>
        )}

        {selectedLanguage !== 'java' && (
          <div>
            <p>该语言的构建参数配置正在开发中...</p>
          </div>
        )}
      </div>
    </div>
  );
};
