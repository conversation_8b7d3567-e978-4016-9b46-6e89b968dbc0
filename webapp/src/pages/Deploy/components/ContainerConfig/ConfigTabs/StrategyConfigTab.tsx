import React, { useMemo } from 'react';
import { 
  Form, Select, Row, Col, Card, Divider, Tooltip, Typography, Badge, InputNumber, Switch, Button 
} from 'antd';
import { InfoCircleOutlined, CheckCircleFilled, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { BlueGreenDeploymentDiagram, CanaryDeploymentDiagram } from '../DeploymentFlow';
import './StrategyConfigTab.less';

const { Text } = Typography;
const { Option } = Select;

interface StrategyConfigTabProps {
  selectedStrategy: string;
  onStrategyChange: (strategy: string) => void;
}

const StrategyConfigTab: React.FC<StrategyConfigTabProps> = React.memo(({ 
  selectedStrategy, 
  onStrategyChange 
}) => {
  // 使用useMemo优化渲染，只有当策略变化时才重新渲染图表
  const DiagramComponent = useMemo(() => {
    return selectedStrategy === 'blueGreen' ? 
      <BlueGreenDeploymentDiagram height={300} /> : 
      <CanaryDeploymentDiagram height={350} />;
  }, [selectedStrategy]);

  const handleCardSelect = (strategy: string) => {
    onStrategyChange(strategy);
    // 隐式设置Form中的策略值
    const formStrategyField = document.querySelector('input[id="strategy"]');
    if (formStrategyField) {
      (formStrategyField as HTMLInputElement).value = strategy;
    }
  };

  // 计算下一个步骤的推荐权重值
  const calculateNextWeight = (fields: any[]) => {
    if (fields.length === 0) return 20;
    
    // 获取当前最后一个步骤的权重值
    const form = (document.querySelector('.ant-form') as any)?.form;
    if (!form) return Math.min(100, (fields.length + 1) * 20);
    
    const lastIndex = fields[fields.length - 1].name;
    const lastWeight = form.getFieldValue(['canary_config', 'steps', lastIndex, 'set_weight']) || 0;
    
    // 计算下一个推荐值，递增20%，但不超过100%
    return Math.min(100, lastWeight + 20);
  };

  return (
    <div className="tab-content">
      <div className="strategy-section">
        {/* 隐藏的表单项，实际值由卡片选择器控制 */}
        <Form.Item
          name="strategy"
          rules={[{ required: true, message: '请选择部署策略' }]}
          hidden
        >
          <Select />
        </Form.Item>

        {/* 卡片式选择器 */}
        <div className="strategy-card-selector">
          <Row gutter={[16, 16]} justify="center">
            <Col xs={24} sm={12}>
              <Card 
                className={`strategy-card ${selectedStrategy === 'blueGreen' ? 'selected' : ''}`}
                onClick={() => handleCardSelect('blueGreen')}
                hoverable
              >
                <div className="strategy-card-header">
                  <div className="strategy-card-title">
                    <Badge color="#1890ff" />
                    <span>蓝绿部署</span>
                    {selectedStrategy === 'blueGreen' && 
                      <CheckCircleFilled className="selected-icon" />
                    }
                  </div>
                </div>
                <div className="strategy-card-content">
                  <div className="strategy-card-desc">
                    <ul>
                      <li>新旧版本同时存在</li>
                      <li>一次性切换全部流量</li>
                      <li>可快速回滚</li>
                    </ul>
                  </div>
                  <div className="strategy-card-icon">
                    <svg width="60" height="40" viewBox="0 0 60 40">
                      <rect x="5" y="5" width="20" height="15" rx="2" fill="#1890ff" opacity="0.8" />
                      <rect x="35" y="20" width="20" height="15" rx="2" fill="#52c41a" opacity="0.8" />
                      <path d="M30,20 L30,20 L40,20" stroke="#8c8c8c" strokeWidth="1.5" strokeDasharray="3,2" />
                      <path d="M25,20 L15,30" stroke="#8c8c8c" strokeWidth="1.5" strokeDasharray="3,2" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12}>
              <Card 
                className={`strategy-card ${selectedStrategy === 'canary' ? 'selected' : ''}`}
                onClick={() => handleCardSelect('canary')}
                hoverable
              >
                <div className="strategy-card-header">
                  <div className="strategy-card-title">
                    <Badge color="#faad14" />
                    <span>金丝雀部署</span>
                    {selectedStrategy === 'canary' && 
                      <CheckCircleFilled className="selected-icon" />
                    }
                  </div>
                </div>
                <div className="strategy-card-content">
                  <div className="strategy-card-desc">
                    <ul>
                      <li>逐步切换流量比例</li>
                      <li>降低发布风险</li>
                      <li>支持灰度测试</li>
                    </ul>
                  </div>
                  <div className="strategy-card-icon">
                    <svg width="60" height="40" viewBox="0 0 60 40">
                      <rect x="5" y="5" width="20" height="15" rx="2" fill="#1890ff" opacity="0.8" />
                      <rect x="5" y="25" width="20" height="10" rx="2" fill="#1890ff" opacity="0.5" />
                      <rect x="35" y="5" width="20" height="5" rx="2" fill="#faad14" opacity="0.8" />
                      <rect x="35" y="15" width="20" height="10" rx="2" fill="#faad14" opacity="0.9" />
                      <rect x="35" y="30" width="20" height="5" rx="2" fill="#52c41a" opacity="0.8" />
                    </svg>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
        
        {/* 策略示意图 */}
        <Card 
          size="small" 
          title={selectedStrategy === 'blueGreen' ? '蓝绿部署流程' : '金丝雀部署流程'} 
          className="strategy-diagram-card"
        >
          <div className="strategy-diagram">
            {DiagramComponent}
          </div>
        </Card>

        {selectedStrategy === 'blueGreen' ? (
          // 蓝绿部署配置
          <div className="strategy-config-section">
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Form.Item
                  name={['bluegreen_config', 'auto_promotion_enabled']}
                  label={
                    <span>
                      自动切换
                      <Tooltip title="是否自动切换到新版本">
                        <InfoCircleOutlined style={{marginLeft: '4px'}} />
                      </Tooltip>
                    </span>
                  }
                  valuePropName="checked"
                >
                  <Select placeholder="选择是否自动切换">
                    <Option value={true}>自动切换</Option>
                    <Option value={false}>手动确认</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name={['bluegreen_config', 'scale_down_delay_seconds']}
                  label={
                    <span>
                      缩容延迟(秒)
                      <Tooltip title="切换完成后多久缩减旧版本">
                        <InfoCircleOutlined style={{marginLeft: '4px'}} />
                      </Tooltip>
                    </span>
                  }
                  rules={[
                    { type: 'integer', min: 0, max: 3600, message: '必须在0-3600秒之间' }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={3600}
                    style={{ width: '100%' }}
                    placeholder="30"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name={['bluegreen_config', 'preview_replica_count']}
                  label={
                    <span>
                      预览副本数
                      <Tooltip title="新版本初始部署的Pod数量">
                        <InfoCircleOutlined style={{marginLeft: '4px'}} />
                      </Tooltip>
                    </span>
                  }
                  rules={[
                    { type: 'integer', min: 1, max: 10, message: '必须在1-10之间' }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={10}
                    style={{ width: '100%' }}
                    placeholder="1"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        ) : (
          // 金丝雀部署配置
          <div className="strategy-config-section">
            <div className="canary-steps-header">
              <Text strong style={{fontSize: '14px'}}>流量分配步骤</Text>
              <Text type="secondary">逐步将流量从旧版本迁移到新版本，每个步骤需要手动确认后继续</Text>
            </div>
            <Form.List name={['canary_config', 'steps']}>
              {(fields, { add, remove }) => (
                <div className="canary-steps-container">
                  {fields.map(({ key, name, ...restField }, index) => (
                    <div key={key} className="canary-step-item" style={{ 
                      padding: '16px', 
                      backgroundColor: index % 2 === 0 ? '#fff' : '#f9f9f9',
                      marginBottom: '8px',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      position: 'relative'
                    }}>
                      <div style={{ 
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: '#1890ff',
                        color: 'white',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: '16px',
                        flexShrink: 0
                      }}>
                        {index + 1}
                      </div>
                      
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ marginRight: '8px' }}>流量</div>
                          <Form.Item
                            {...restField}
                            name={[name, 'set_weight']}
                          style={{ marginBottom: 0, width: '100px' }}
                            rules={[
                              { required: true, message: '请输入流量权重' },
                            { type: 'integer', min: 1, max: 100, message: '1-100之间' }
                            ]}
                          noStyle
                          >
                            <InputNumber
                              min={1}
                              max={100}
                              placeholder="20"
                            style={{ width: '80px' }}
                            />
                          </Form.Item>
                        <span style={{ marginLeft: '8px' }}>%</span>
                      </div>
                      
                      <div style={{ 
                        marginLeft: '32px', 
                        backgroundColor: '#f6ffed', 
                        border: '1px solid #b7eb8f',
                        color: '#52c41a',
                        padding: '4px 12px',
                        borderRadius: '16px',
                        fontSize: '12px',
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                          <Form.Item
                            {...restField}
                            name={[name, 'require_manual_approval']}
                            valuePropName="checked"
                            initialValue={true}
                          style={{ marginBottom: 0, display: 'none' }}
                          >
                            <Switch 
                            checkedChildren="需确认" 
                            unCheckedChildren="自动"
                              defaultChecked
                              disabled 
                            />
                          </Form.Item>
                        <span>需确认</span>
                        <Tooltip title="达到此权重后需要手动批准才能进入下一步骤">
                          <InfoCircleOutlined style={{ marginLeft: '4px', color: '#52c41a' }} />
                        </Tooltip>
                      </div>
                      
                          <Button 
                            type="text" 
                            danger
                            icon={<DeleteOutlined />} 
                            onClick={() => remove(name)}
                            disabled={fields.length <= 1}
                        style={{ 
                          position: 'absolute',
                          right: '16px',
                          top: '50%',
                          transform: 'translateY(-50%)'
                        }}
                      />
                    </div>
                  ))}

                    <Button
                      type="dashed"
                      onClick={() => add({
                        set_weight: calculateNextWeight(fields),
                        require_manual_approval: true
                      })}
                      icon={<PlusOutlined />}
                    style={{ width: '100%', marginTop: '12px' }}
                    >
                      添加步骤
                    </Button>
                </div>
              )}
            </Form.List>
          </div>
        )}
      </div>
    </div>
  );
});

export default StrategyConfigTab; 