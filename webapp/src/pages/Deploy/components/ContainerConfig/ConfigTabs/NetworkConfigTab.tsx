import React from 'react';
import { 
  Form, Input, InputN<PERSON>ber, Select, Button, Card, Typography, 
  Divider, Toolt<PERSON>, Row, Col, Space, Radio, Collapse, Tag
} from 'antd';
import { 
  InfoCircleOutlined, PlusOutlined, DeleteOutlined,
  GlobalOutlined, LockOutlined, DatabaseOutlined,
  ArrowRightOutlined, SettingOutlined, ApiOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;
const { Option } = Select;

interface NetworkConfigTabProps {
  addPort: () => void;
  removePort: (index: number) => void;
}

const NetworkConfigTab: React.FC<NetworkConfigTabProps> = React.memo(({ 
  addPort, 
  removePort 
}) => {
  // 添加常用端口配置
  const addCommonPort = (portType: string) => {
    const form = Form.useFormInstance();
    const ports = form.getFieldValue('ports') || [];
    
    let newPort = {};
    switch (portType) {
      case 'http':
        newPort = { 
          name: 'http', 
          container_port: 8080, 
          service_port: 80, 
          protocol: 'TCP' 
        };
        break;
      case 'https':
        newPort = { 
          name: 'https', 
          container_port: 8443, 
          service_port: 443, 
          protocol: 'TCP' 
        };
        break;
      case 'db':
        newPort = { 
          name: 'database', 
          container_port: 3306, 
          service_port: 3306, 
          protocol: 'TCP' 
        };
        break;
      default:
        newPort = { 
          name: `port-${ports.length + 1}`, 
          container_port: 8080, 
          service_port: 8080, 
          protocol: 'TCP' 
        };
    }
    
    form.setFieldsValue({ 
      ports: [...ports, newPort]
    });
    
    addPort();
  };

  return (
    <div className="tab-content network-config-tab">
      <Card 
        className="network-card"
        bordered={false}
        bodyStyle={{ padding: '20px', backgroundColor: '#f9fafc', borderRadius: '8px' }}
      >
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '16px',
          backgroundColor: '#f0f7ff',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #1890ff'
        }}>
          <ApiOutlined style={{ fontSize: '18px', color: '#1890ff', marginRight: '10px' }} />
          <Title level={5} style={{ margin: 0, color: '#0050b3' }}>端口映射</Title>
        </div>
        
        <Form.List name="ports">
          {(fields, { add, remove }) => (
            <div className="ports-container">
              {fields.length === 0 ? (
                <div 
                  className="empty-ports" 
                  style={{ 
                    padding: '24px', 
                    textAlign: 'center', 
                    backgroundColor: '#f0f2f5', 
                    borderRadius: '6px',
                    border: '1px dashed #d9d9d9'
                  }}
                >
                  <Text type="secondary">尚未配置端口，点击下方按钮添加自定义端口</Text>
                </div>
              ) : (
                <div style={{ backgroundColor: '#fff', borderRadius: '6px', padding: '8px 16px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }}>
                  {fields.map(({ key, name, ...restField }, index) => (
                    <div key={key} className="port-row">
                      <Row gutter={[16, 0]} align="middle" style={{ padding: '12px 0' }}>
                        <Col flex="150px">
                        <Form.Item
                          {...restField}
                          name={[name, 'name']}
                            label={<span style={{ fontSize: '13px' }}>名称</span>}
                          rules={[{ required: true, message: '请输入名称' }]}
                          style={{ marginBottom: '8px' }}
                        >
                            <Input size="middle" placeholder="如: http" />
                        </Form.Item>
                      </Col>
                        <Col flex="130px">
                        <Form.Item
                          {...restField}
                          name={[name, 'container_port']}
                            label={<span style={{ fontSize: '13px' }}>容器端口</span>}
                            tooltip={{ title: "容器内监听的端口", color: 'blue' }}
                          rules={[{ required: true, message: '请输入容器端口' }]}
                          style={{ marginBottom: '8px' }}
                        >
                          <InputNumber 
                              size="middle" 
                            min={1} 
                            max={65535} 
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col flex="30px" style={{ textAlign: 'center', marginTop: '8px' }}>
                          <ArrowRightOutlined style={{ color: '#1890ff' }} />
                      </Col>
                        <Col flex="130px">
                        <Form.Item
                          {...restField}
                          name={[name, 'service_port']}
                            label={<span style={{ fontSize: '13px' }}>服务端口</span>}
                            tooltip={{ title: "集群内暴露的服务端口", color: 'blue' }}
                          rules={[{ required: true, message: '请输入服务端口' }]}
                          style={{ marginBottom: '8px' }}
                        >
                          <InputNumber 
                              size="middle" 
                            min={1} 
                            max={65535} 
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                        <Col flex="120px">
                        <Form.Item
                          {...restField}
                          name={[name, 'protocol']}
                            label={<span style={{ fontSize: '13px' }}>协议</span>}
                          style={{ marginBottom: '8px' }}
                        >
                            <Select size="middle" style={{ width: '100%' }}>
                              <Option value="TCP">
                                <Tag color="processing" style={{ marginRight: 0 }}>TCP</Tag>
                              </Option>
                              <Option value="UDP">
                                <Tag color="warning" style={{ marginRight: 0 }}>UDP</Tag>
                              </Option>
                          </Select>
                        </Form.Item>
                      </Col>
                        <Col flex="auto" style={{ textAlign: 'right' }}>
                          <Tooltip title={fields.length <= 1 ? "至少需要一个端口" : "删除此端口"}>
                        <Button 
                          danger 
                          type="text" 
                              size="middle" 
                          icon={<DeleteOutlined />} 
                          onClick={() => removePort(name)}
                          disabled={fields.length <= 1}
                              style={{ borderRadius: '50%', width: '32px', height: '32px' }}
                        />
                          </Tooltip>
                      </Col>
                    </Row>
                      {index < fields.length - 1 && <Divider style={{ margin: '0' }} dashed />}
                    </div>
                  ))}
                  </div>
              )}
              
              <Button 
                type="dashed" 
                block 
                icon={<PlusOutlined />} 
                onClick={addPort}
                style={{ marginTop: '16px', height: '40px' }}
              >
                添加自定义端口
              </Button>
            </div>
          )}
        </Form.List>
        
        <div className="section-header" style={{ 
          display: 'flex', 
          alignItems: 'center',
          marginTop: '32px',
          marginBottom: '16px',
          backgroundColor: '#f7f7f7',
          padding: '10px 16px',
          borderRadius: '6px',
          borderLeft: '4px solid #722ed1'
        }}>
          <SettingOutlined style={{ fontSize: '18px', color: '#722ed1', marginRight: '10px' }} />
          <Title level={5} style={{ margin: 0, color: '#531dab' }}>高级设置</Title>
        </div>
        
        <Collapse 
          ghost 
          className="network-advanced-settings"
          style={{ backgroundColor: '#fff', borderRadius: '6px', padding: '0 8px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)' }}
        >
          <Collapse.Panel 
            header={<Text strong>服务发现配置</Text>} 
            key="service-discovery"
          >
            <Form.Item 
              name="service_type" 
              label="服务类型"
              style={{ marginBottom: '8px' }}
            >
              <Radio.Group buttonStyle="solid">
                <Radio.Button value="ClusterIP">
                  <Space>
                    <span>集群内部</span>
                    <Tooltip title="仅在集群内部可访问">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                </Radio.Button>
                <Radio.Button value="NodePort">
                  <Space>
                    <span>节点端口</span>
                    <Tooltip title="通过节点IP和端口访问">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                </Radio.Button>
                <Radio.Button value="LoadBalancer">
                  <Space>
                    <span>负载均衡</span>
                    <Tooltip title="使用云服务商的负载均衡">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Collapse.Panel>
        </Collapse>
        
        <div className="network-tips" style={{ marginTop: '24px', fontSize: '13px', padding: '12px', backgroundColor: '#e6f7ff', borderRadius: '6px', border: '1px solid #91d5ff' }}>
          <Text type="secondary">
            <InfoCircleOutlined style={{marginRight: '8px', color: '#1890ff' }} />
            提示: 容器端口是容器内部程序监听的端口，服务端口是集群内访问的端口
          </Text>
        </div>
      </Card>
    </div>
  );
});

export default NetworkConfigTab; 